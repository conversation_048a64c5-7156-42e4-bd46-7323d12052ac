<script lang="ts">
  export let content: string;
  export let timestamp: string;
  export let user_id: string;
  export let user_name: string;
</script>
<div class="flex justify-end items-start gap-2 w-full">
  <div class="flex flex-col items-end">
    <div class="bg-primary text-primary-foreground rounded-xl px-4 py-2 max-w-[70vw] shadow">
      <span class="font-semibold text-sm">{user_name}</span>
      <div class="text-base">{content}</div>
      <span class="text-xs text-muted-foreground">{new Date(timestamp).toLocaleTimeString()}</span>
    </div>
  </div>
  <img src="https://i.pravatar.cc/40?u={user_id}" alt="avatar" class="rounded-full w-10 h-10 border" />
</div> 