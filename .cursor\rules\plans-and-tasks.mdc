---
description: While Planning, Use this planning and task creation format
globs: 
alwaysApply: false
---
# <PERSON><PERSON>ak Model Selection System: Plans and Tasks

## Tasks and Plans Tracking

- While Planning and Execution create the following within `readme/tasks/`:
  - `plans_<ddMMMyyHHMM>.md` (e.g., `plans_05May251250.md`)
  - `tasks_<ddMMMyyHHMM>.md` (e.g., `tasks_05May251250.md`)
- **Timestamp Format:** `dd` (day), `MMM` (3-letter month), `yy` (year), `HH` (hour 24h), `MM` (minute). **Do not** add a 'd' prefix to the day.
- The tasks file must have checkboxes to track progress.