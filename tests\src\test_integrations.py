import asyncio
import json
import sys
import os
from pathlib import Path
from src.core import IntegrationsManager
from src.agent import AgentModule

# Add parent directory to Python path to allow imports
current_dir = Path(__file__).parent
sys.path.append(str(current_dir.parent))


async def test_integration_manager():
    """Test the integration manager health check functionality."""
    print("\n=== Testing Integration Manager ===")
    
    # Create integration manager
    integration_manager = IntegrationsManager()
    
    # Run health checks
    print("Running integration health checks...")
    integration_status = await integration_manager.check_all_integrations()
    
    # Print status
    print(f"Tools status: {integration_status['tools']['status']}")
    if not integration_status['tools']['status']:
        print(f"Tools error: {integration_status['tools']['error']}")
    
    print(f"MCP servers: {len(integration_status['mcp_servers'])}")
    for server in integration_status['mcp_servers']:
        print(f"- {server['url']}: {'Available' if server['status'] else 'Unavailable'}")
    
    print("Integration status updated successfully.")
    
    # Read integration status from file
    print("\nReading integration status from file...")
    status = IntegrationsManager.get_integration_status()
    print(f"Last updated: {status['last_updated']}")
    
    return integration_status

def test_agent_module():
    """Test the agent module with synchronous get_agent()."""
    print("\n=== Testing Agent Module ===")
    
    # Create agent modules
    print("Creating Groq agent...")
    try:
        groq_agent_module = AgentModule("groq/llama-3.3-70b-versatile")
        groq_agent = groq_agent_module.get_agent()
        print("Groq agent created successfully.")
        
        # Print agent configuration
        print(f"Model: {groq_agent_module.model_id}")
        print(f"Tools: {len(groq_agent.tools)}")
        print(f"MCP servers: {len(groq_agent.mcp_servers)}")
    except Exception as e:
        print(f"Error creating Groq agent: {e}")
    
    print("\nCreating OpenRouter agent...")
    try:
        or_agent_module = AgentModule("openrouter/anthropic/claude-3.5-sonnet")
        or_agent = or_agent_module.get_agent()
        print("OpenRouter agent created successfully.")
        
        # Print agent configuration
        print(f"Model: {or_agent_module.model_id}")
        print(f"Tools: {len(or_agent.tools)}")
        print(f"MCP servers: {len(or_agent.mcp_servers)}")
    except Exception as e:
        print(f"Error creating OpenRouter agent: {e}")

async def main():
    # Test integration manager
    integration_status = await test_integration_manager()
    
    # Test agent module
    test_agent_module()
    
    # Print final message
    print("\n=== Testing Complete ===")
    print(f"Tools available: {integration_status['tools']['status']}")
    print(f"MCP servers available: {len([s for s in integration_status['mcp_servers'] if s['status']])}")

if __name__ == "__main__":
    asyncio.run(main()) 