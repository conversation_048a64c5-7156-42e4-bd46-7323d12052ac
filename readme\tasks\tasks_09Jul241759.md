# Tasks: Session-wise Chat History Retrieval with Separate DB

## [ ] 1. Design Database Structure
- [ ] Define schema for `.chat_session_messages.sqlite` with `session_id`, `role`, `timestamp`, `content` fields
- [ ] Ensure schema is compatible with current message format

## [ ] 2. Create DB Directory and File
- [ ] Create `db/` directory if it does not exist
- [ ] Initialize `.chat_session_messages.sqlite` in `db/` with the new schema

## [ ] 3. Update Database Access Layer
- [ ] Implement `SessionDatabase` class for session-based operations
    - [ ] `add_message(session_id, message)`
    - [ ] `get_messages(session_id)`
- [ ] Ensure thread safety and async compatibility

## [ ] 4. Integrate with FastAPI Endpoints
- [ ] Add or extend endpoints to support session-based chat
    - [ ] `/chat/{session_id}/` for session-specific chat history
- [ ] Ensure backward compatibility with `/chat/`

## [ ] 5. Migration and Coexistence
- [ ] Ensure both old and new systems can operate in parallel
- [ ] Document migration/copying of messages if needed

## [x] 6. Update Frontend for Session Support
- [ ] Update `chatapp.html` to allow users to select or enter a session ID
- [ ] Update `chat_app.ts` to use the session ID in all chat requests
- [ ] Update chat history retrieval to use the new session-based endpoint
- [ ] Ensure UI updates when session ID changes (loads correct chat history)
- [ ] Add clear instructions or UI hints for session-based chat
- [ ] Test with multiple sessions to ensure isolation and correct retrieval

## [ ] Implement Gemini embeddings in ToolManager with type hints and Google-style docstrings
- [ ] Add error handling for missing API keys and failed requests
- [ ] Update src/integrations/functional_dependencies.md to document new/changed functions
- [ ] Update readme/deps/module_dependencies.md to reflect new/changed dependencies
- [ ] Run `uv run ruff check --fix src/integrations/tool_module.py` and address any issues
- [ ] Add/Update tests for embedding functionality if not present