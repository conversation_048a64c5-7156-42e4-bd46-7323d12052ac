# Plan: Session-wise Chat History Retrieval with Separate DB

## Goal
Enable session-wise chat history retrieval and storage using a dedicated SQLite database file (`.chat_session_messages.sqlite`) in the `db` directory, without disrupting the current chat system.

## Steps

1. **Design Database Structure**
   - Define schema for `.chat_session_messages.sqlite` to store messages with a `session_id` field.
   - Ensure compatibility with the existing message format (role, timestamp, content).

2. **Create DB Directory and File**
   - Add a `db/` directory if it doesn't exist.
   - Initialize `.chat_session_messages.sqlite` in `db/` with the new schema.

3. **Update Database Access Layer**
   - Implement a new `SessionDatabase` class for session-based operations.
   - Methods:
     - `add_message(session_id, message)`
     - `get_messages(session_id)`
   - Ensure thread safety and async compatibility as in the current system.

4. **Integrate with FastAPI Endpoints**
   - Add new endpoints or extend existing ones to support session-based chat retrieval and storage.
   - Example: `/chat/{session_id}/` for session-specific chat history.
   - Ensure backward compatibility with the current `/chat/` endpoint.

5. **Migration and Coexistence**
   - Ensure both the old and new systems can operate in parallel.
   - Document how to migrate or copy messages if needed.

6. **Testing**
   - Write tests for session-based storage and retrieval.
   - Ensure no regression in the current chat functionality.

7. **Documentation**
   - Update README and API docs to describe session-based chat history.
   - Add usage examples for new endpoints.

## Notes
- Use the timestamp format as per the plans-and-tasks rule for all new files.
- Do not disrupt the current chat message flow; add session support as an extension.
- Ensure all new code is consistent with project structure and best practices. 