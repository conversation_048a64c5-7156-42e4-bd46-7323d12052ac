<script lang="ts">
import { onMount, onDestroy } from 'svelte';
import { writable } from 'svelte/store';

const mouse = writable({ x: 0, y: 0 });
let spotlightEl: HTMLDivElement;
let mousePosition = { x: 0, y: 0 };

// Subscribe to the store to get updates
$: mousePosition = $mouse;

function handleMouseMove(e: MouseEvent) {
  mouse.set({ x: e.clientX, y: e.clientY });
}

onMount(() => {
  window.addEventListener('mousemove', handleMouseMove);
  return () => window.removeEventListener('mousemove', handleMouseMove);
});
</script>

<style>
.spotlight-bg {
  pointer-events: none;
  position: fixed;
  inset: 0;
  z-index: 0;
}
</style>

<div bind:this={spotlightEl} class="spotlight-bg">
  <svg width="100vw" height="100vh" style="position: absolute; inset: 0; width: 100vw; height: 100vh;">
    <defs>
      <radialGradient id="spotlight-gradient" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stop-color="rgba(255,255,255,0.15)" />
        <stop offset="60%" stop-color="rgba(255,255,255,0.05)" />
        <stop offset="100%" stop-color="rgba(0,0,0,0)" />
      </radialGradient>
    </defs>
    <circle
      cx={mousePosition.x}
      cy={mousePosition.y}
      r="180"
      fill="url(#spotlight-gradient)"
    />
  </svg>
</div>
<slot />