<script lang="ts">
	import { cn } from '$lib/utils';
	export let className: string | undefined = undefined;
	export let type: string | undefined = undefined;
	export let id: string | undefined = undefined;
	export let placeholder: string | undefined = undefined;
	export let value: string | undefined = undefined;
	export let disabled: boolean = false;
</script>

<input
	class={
		cn(
			"block w-full rounded-md border border-input bg-background px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
			className
		)
	}
	type={type}
	id={id}
	placeholder={placeholder}
	value={value}
	disabled={disabled}
	{...$$restProps}
/> 