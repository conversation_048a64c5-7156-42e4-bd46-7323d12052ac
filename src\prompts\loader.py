"""
Prompt loader module for the KCRH agent.
This module provides functions to load and manage prompts.
"""
import os
from pathlib import Path
from typing import Dict, Optional, Any
import logging
from src.integrations.mem0_module import Mem0Manager

# Define the base directory for prompts
PROMPTS_DIR = Path(__file__).parent
logger = logging.getLogger(__name__)

def load_prompt(prompt_name: str) -> str:
    """
    Load a prompt from a file.
    
    Args:
        prompt_name: Name of the prompt file without extension
        
    Returns:
        The content of the prompt file as a string
        
    Raises:
        FileNotFoundError: If the prompt file doesn't exist
    """
    prompt_path = PROMPTS_DIR / f"{prompt_name}.md"
    
    if not prompt_path.exists():
        raise FileNotFoundError(f"Prompt file not found: {prompt_path}")
    
    with open(prompt_path, "r", encoding="utf-8") as f:
        return f.read()

def get_system_prompt() -> str:
    """
    Get the system prompt for the Vahan Sahayak agent.
    
    Returns:
        The system prompt as a string
    """
    return load_prompt("system_prompt")

def get_prompt_variables(extra: Optional[Dict[str, Any]] = None) -> Dict[str, str]:
    """
    Get variables that can be used in prompts, including agent, org, and context.
    
    Args:
        extra: Optional dict to override/add variables
    
    Returns:
        A dictionary of variable names and their values
    """
    variables = {
        "agent_name": "Vahan Sahayak",
        "agent_name_meaning": "Vehicle Assistant",
        "organization_name": os.getenv("ORGANIZATION_NAME", "the organization"),
        "telecaller_table": "telecaller_customer_guidance",
        "user_feedback_table": "user_feedback",
        "chat_history_table": "chat_history",
        "document_rows_table": "document_rows",
        "documents_768_table": "documents_768",
        "document_metadata_table": "document_metadata",
        "image_metadata_table": "image_metadata",
        "telematics_tools": "get_vehicle_software_version, get_gps_data, get_network_data, get_bms_overview, get_mcu_overview, get_bcm_overview, get_active_faults, current_vehicle_state, get_network_timestamp, get_all_ecu_versions",
    }
    if extra:
        variables.update(extra)
    return variables

def format_prompt(prompt: str, variables: Optional[Dict[str, str]] = None) -> str:
    """
    Format a prompt with variables.
    
    Args:
        prompt: The prompt template
        variables: A dictionary of variable names and their values
        
    Returns:
        The formatted prompt
    """
    if variables is None:
        variables = get_prompt_variables()
    
    # Replace variables in the prompt
    for key, value in variables.items():
        prompt = prompt.replace(f"{{{key}}}", str(value))
    
    return prompt

def get_formatted_system_prompt(extra: Optional[Dict[str, Any]] = None) -> str:
    """
    Get the formatted system prompt with variables replaced.
    
    Args:
        extra: Optional dict to override/add variables
    
    Returns:
        The formatted system prompt
    """
    prompt = get_system_prompt()
    return format_prompt(prompt, get_prompt_variables(extra))

# --- Markdown helpers for agent response formatting ---
def format_image_table(image_rows: list[tuple[str, str]]) -> str:
    """
    Format a markdown table for images as per system_prompt.md.
    
    Args:
        image_rows: List of (image_name, image_url) tuples
    
    Returns:
        Markdown table string
    """
    table = "| Image Name and Details | Image |\n| ---------------------- | ----- |\n"
    for name, url in image_rows:
        table += f"| {name} | ![ {name}]({url}) |\n"
    return table

def format_markdown_link(text: str, url: str) -> str:
    """
    Format a markdown link.
    
    Args:
        text: Link text
        url: URL
    
    Returns:
        Markdown link string
    """
    return f"[{text}]({url})"

# --- Agent flow prompt loaders (stubs, can be expanded as needed) ---
def get_telecaller_prompt() -> str:
    """
    Load and format the telecaller step prompt.
    
    Returns:
        The formatted telecaller prompt
    """
    return load_prompt("telecaller_prompt") if (PROMPTS_DIR / "telecaller_prompt.md").exists() else ""

def get_rag_prompt() -> str:
    """
    Load and format the RAG step prompt.
    
    Returns:
        The formatted RAG prompt
    """
    return load_prompt("rag_prompt") if (PROMPTS_DIR / "rag_prompt.md").exists() else ""

def get_rule_engine_prompt() -> str:
    """
    Load and format the Rule Engine step prompt.
    
    Returns:
        The formatted Rule Engine prompt
    """
    return load_prompt("rule_engine_prompt") if (PROMPTS_DIR / "rule_engine_prompt.md").exists() else ""

def get_system_prompt_with_chat_memory(user_id: str, user_query: str) -> str:
    """
    Get the system prompt with relevant user memory (if available) appended as context.

    Args:
        user_id: The user identifier.
        user_query: The current user query.
    Returns:
        The system prompt string with user memory context if available.
    """
    system_prompt = get_system_prompt()
    mem_manager = Mem0Manager()
    try:
        memory_result = mem_manager.retrieve(user_query, user_id)
        logger.info(f"Retrieved memory for user_id={user_id}, query='{user_query}': {memory_result}")
    except Exception as e:
        logger.error(f"Error retrieving memory for user_id={user_id}: {e}")
        memory_result = None

    # Format memory context if available
    memory_context = ""
    if memory_result and getattr(memory_result, 'results', None):
        # If result is a dict with 'results', format them
        memories = memory_result['results'] if isinstance(memory_result, dict) else memory_result
        if memories:
            memory_context = "\n\n## User Memory Context\n" + "\n".join(
                f"- {m.get('memory', str(m))}" for m in memories
            )
    elif memory_result and isinstance(memory_result, list):
        # If result is a list
        memory_context = "\n\n## User Memory Context\n" + "\n".join(
            f"- {str(m)}" for m in memory_result
        )
    # else: no memory found or error

    return system_prompt + memory_context
