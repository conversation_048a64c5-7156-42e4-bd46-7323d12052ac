<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON> Instant</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    main {
      max-width: 700px;
    }
    #conversation .user::before {
      content: 'You asked: ';
      font-weight: bold;
      display: block;
    }
    #conversation .model::before {
      content: 'AI Response: ';
      font-weight: bold;
      display: block;
    }
    #spinner {
      opacity: 0;
      transition: opacity 500ms ease-in;
      width: 30px;
      height: 30px;
      border: 3px solid #222;
      border-bottom-color: transparent;
      border-radius: 50%;
      animation: rotation 1s linear infinite;
    }
    @keyframes rotation {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    #spinner.active {
      opacity: 1;
    }
  </style>
</head>
<body>
  <main class="border rounded mx-auto my-5 p-4">
    <h1><PERSON><PERSON> Instant</h1>
    <p>Ask me anything...</p>
    <div id="conversation" class="px-2"></div>
    <div class="d-flex justify-content-center mb-3">
      <div id="spinner"></div>
    </div>
    <form method="post">
      <div class="mb-2 d-flex align-items-center">
        <label for="user-id-input" class="form-label me-2 mb-0">User ID</label>
        <input id="user-id-input" name="user_id" class="form-control me-2" style="max-width: 200px;" />
        <button type="button" class="btn btn-outline-secondary btn-sm" id="regenerate-user-id">Regenerate</button>
      </div>
      <div class="mb-2 d-flex align-items-center">
        <label for="session-id-input" class="form-label me-2 mb-0">Session ID</label>
        <input id="session-id-input" name="session_id" class="form-control me-2" style="max-width: 200px;" />
        <button type="button" class="btn btn-outline-secondary btn-sm" id="regenerate-session-id">Regenerate</button>
      </div>
      <div class="mb-2 d-flex align-items-center">
        <label for="vin-input" class="form-label me-2 mb-0">VIN Number</label>
        <input id="vin-input" name="vin" class="form-control me-2" style="max-width: 200px;" placeholder="Enter VIN" />
        <span id="vin-status" class="badge bg-secondary">Not Validated</span>
      </div>
      <div class="mb-2 d-flex align-items-center">
        <label for="phone-input" class="form-label me-2 mb-0">Phone Number</label>
        <input id="phone-input" name="phone" class="form-control me-2" style="max-width: 200px;" placeholder="Enter Phone" />
        <span id="phone-status" class="badge bg-secondary">Not Validated</span>
      </div>
      <div class="mb-2 d-flex align-items-center">
        <label for="order-id-input" class="form-label me-2 mb-0">Order ID</label>
        <input id="order-id-input" name="order_id" class="form-control me-2" style="max-width: 200px;" placeholder="Enter Order ID" />
        <span id="order-id-status" class="badge bg-secondary">Not Validated</span>
      </div>
      <div class="mb-2">
        <label for="model-select" class="form-label">Model</label>
        <select id="model-select" name="model" class="form-select">
          <optgroup label="Groq">
            <option value="groq/llama-3.3-70b-versatile">Groq: Llama 3.3 70B - Meta's versatile large language model</option>
            <option value="groq/meta-llama/llama-4-maverick-17b-128e-instruct">Groq: Llama 4 Maverick 17B - Meta's latest instruction-tuned model</option>
            <option value="groq/deepseek-r1-distill-llama-70b">Groq: DeepSeek R1 Distill Llama 70B - DeepSeek's compact instruction-tuned model</option>
          </optgroup>
          <optgroup label="OpenRouter">
            <option value="openrouter/anthropic/claude-3.5-sonnet">OpenRouter: Claude 3.5 Sonnet - Anthropic's powerful and efficient model</option>
            <option value="openrouter/anthropic/claude-3.7-sonnet">OpenRouter: Claude 3.7 Sonnet - Anthropic's latest and most capable model</option>
            <option value="openrouter/meta-llama/llama-3.1-8b-instruct">OpenRouter: Llama 3.1 8B - Meta's compact instruction-tuned model</option>
          </optgroup>
        </select>
      </div>
      <div class="mb-2">
        <label for="temperature-slider" class="form-label">Temperature: <span id="temperature-value">0.2</span></label>
        <input type="range" class="form-range" min="0" max="2" step="0.01" value="0.2" id="temperature-slider" name="temperature" />
      </div>
      <div class="mb-2 form-check">
        <input class="form-check-input" type="checkbox" id="memory-toggle" name="enable_memory" checked>
        <label class="form-check-label" for="memory-toggle">Enable Chat Memory</label>
      </div>
      <input id="prompt-input" name="prompt" class="form-control"/>
      <div class="d-flex justify-content-end">
        <button class="btn btn-primary mt-2">Send</button>
      </div>
    </form>
    <div id="error" class="d-none text-danger">
      Error occurred, check the browser developer console for more information.
    </div>
  </main>
</body>
</html>
<script src="https://cdnjs.cloudflare.com/ajax/libs/typescript/5.6.3/typescript.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script type="module">
  // to let me write TypeScript, without adding the burden of npm we do a dirty, non-production-ready hack
  // and transpile the TypeScript code in the browser
  // this is (arguably) A neat demo trick, but not suitable for production!
  async function loadTs() {
    const response = await fetch('../chat_app.ts');
    const tsCode = await response.text();
    const jsCode = window.ts.transpile(tsCode, { target: "es2015" });
    let script = document.createElement('script');
    script.type = 'module';
    script.text = jsCode;
    document.body.appendChild(script);
  }

  loadTs().catch((e) => {
    console.error(e);
    document.getElementById('error').classList.remove('d-none');
    document.getElementById('spinner').classList.remove('active');
  });
</script>