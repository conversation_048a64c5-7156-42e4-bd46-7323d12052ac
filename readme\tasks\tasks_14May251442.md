# Tasks: Implement get_rule_id_from_user_query Tool and Update System Prompt (14 May 25 14:42)

- [ ] Implement `get_rule_id_from_user_query(user_query: str)` in `tool_module.py`
    - [ ] Use `match_rule_engine_768` RPC on `rule_engine_768` table
    - [ ] Return most relevant rule ID(s) for a user query
    - [ ] Add a detailed docstring for AI Agent
- [ ] Add tests or example usage for the new tool
- [ ] Update `system_prompt.md` to document the new tool and its purpose
- [ ] Validate by simulating a user query and confirming rule ID retrieval
- [ ] Update documentation/readme as needed 