import os
import logging
from mem0 import Memory
from typing import Any, Dict, List, Optional
from dotenv import load_dotenv

load_dotenv()
SUPABASE_URL = os.getenv("SUPABASE_CONNECTION_URL")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
# Logging setup as per rules
os.makedirs("./logs/integrations", exist_ok=True)
info_log_path = "./logs/integrations/info.log"
debug_log_path = "./logs/integrations/debug.log"
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(message)s",
    handlers=[
        logging.FileHandler(info_log_path),
        logging.FileHandler(debug_log_path),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Gemini 768 config (from config-add.md)
config = {
    "embedder": {
        "provider": "gemini",
        "config": {
            "model": "models/text-embedding-004",
            "api_key": GEMINI_API_KEY,
            "embedding_dims": 768,
        }
    },
     "vector_store": {
        "provider": "supabase",
        "config": {
            "connection_string": SUPABASE_URL,
            "collection_name": "mem0_memories768",
            "embedding_model_dims": 768,
            "index_method": "hnsw",  # hnsw is the best for 768
            "index_measure": "cosine_distance"  # Optional: defaults to "cosine_distance"
        }
    }
}

class Mem0Manager:
    """Simple Mem0 memory manager using Gemini 768 config."""
    def __init__(self) -> None:
        self.memory = Memory.from_config(config)
        logger.info("Initialized Mem0Manager with Gemini 768 config.")

    def add(self, user_id: str, messages: List[Dict[str, str]], metadata: Optional[Dict[str, Any]] = None) -> Any:
        """Store conversation messages as a memory for a user.

        Args:
            user_id: The user identifier.
            messages: List of message dicts with 'role' and 'content'.
            metadata: Optional metadata dict.
        Returns:
            Result from Mem0 add operation.
        """
        logger.info(f"Adding memory for user_id={user_id}")
        result = self.memory.add(messages, user_id=user_id, metadata=metadata) \
        if metadata else self.memory.add(messages, user_id=user_id)
        logger.debug(f"Add result: {result}")
        return result

    def retrieve(self, query: str, user_id: str) -> Any:
        """Retrieve/search memories for a user by query.

        Args:
            query: The search query string.
            user_id: The user identifier.
        Returns:
            Search results from Mem0.
        """
        logger.info(f"Retrieving memories for user_id={user_id}, query='{query}'")
        result = self.memory.search(query=query, user_id=user_id)
        logger.debug(f"Retrieve result: {result}")
        return result

    def update(self, memory_id: str, data: str) -> Any:
        """Update a memory by its ID.

        Args:
            memory_id: The memory identifier.
            data: The new data/content for the memory.
        Returns:
            Result from Mem0 update operation.
        """
        logger.info(f"Updating memory_id={memory_id}")
        result = self.memory.update(memory_id=memory_id, data=data)
        logger.debug(f"Update result: {result}")
        return result

    def delete(self, memory_id: Optional[str] = None, user_id: Optional[str] = None) -> None:
        """Delete a memory by ID or all memories for a user.

        Args:
            memory_id: The memory identifier to delete (optional).
            user_id: The user identifier to delete all memories for (optional).
        """
        if memory_id:
            logger.info(f"Deleting memory_id={memory_id}")
            self.memory.delete(memory_id=memory_id)
        elif user_id:
            logger.info(f"Deleting all memories for user_id={user_id}")
            self.memory.delete_all(user_id=user_id)
        else:
            logger.warning("No memory_id or user_id provided for delete operation.")

# Example usage
if __name__ == "__main__": 
    # breakpoint()
    m = Mem0Manager()
    user_id = "test_user"
    messages = [
        {"role": "user", "content": "I like Polar Bears"},
        {"role": "assistant", "content": "I like Polar Bears too."}
    ]
    m.add(user_id, messages)
    print(m.retrieve("Polar Bears", user_id))


