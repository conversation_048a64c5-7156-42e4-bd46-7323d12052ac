# Vahan Sahayak Integration Health Check Plan

## Overview
Modify the agent system to separate integration health checks from the agent initialization process. Store health check results in a JSON file and make get_agent() a synchronous method.

## Architecture Changes
1. Create a health check system in core.py that runs before agent initialization
2. Store the health status of tools and MCP servers in integrations.json
3. Modify agent.py to read from integrations.json instead of performing health checks
4. Make get_agent() synchronous

## Implementation Steps
1. Create a new IntegrationsManager class in core.py that:
   - Performs health checks on tools and MCP servers
   - Stores results in integrations.json
   - Has methods to update and read integration status

2. Modify src/config/integrations.json structure:
   ```json
   {
     "last_updated": "timestamp",
     "tools": {
       "status": true|false,
       "error": "error message if any"
     },
     "mcp_servers": [
       {
         "url": "server_url",
         "status": true|false
       }
     ]
   }
   ```

3. Modify agent.py:
   - Update get_agent() to be synchronous
   - Read integration status from integrations.json
   - Initialize tools and MCP servers based on stored status

4. Create endpoints in core.py:
   - /integrations/status to get current integration status
   - /integrations/refresh to trigger a refresh of integration status

## Testing Plan
1. Test the integration status endpoints
2. Test agent initialization without health checks
3. Verify system behavior when integrations are unavailable 