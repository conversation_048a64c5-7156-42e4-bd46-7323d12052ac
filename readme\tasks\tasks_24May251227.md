# Tasks: Implement order_list Tool and Endpoint (24 May 2025, 12:27)

- [x] Add `get_order_list(phone_number: str)` to `ToolManager` in `tool_module.py`
- [x] Implement API call to get tenant UUID from phone number
- [x] Implement API call to get order list from tenant UUID
- [x] Handle errors and edge cases (invalid phone, no orders, etc.)
- [x] Add FastAPI endpoint `/order_list` in `src/core.py`
- [ ] Register tool for agent use (if needed)
- [ ] Add/update tests for tool and endpoint
- [ ] Update documentation if needed 