from pydantic_ai import Agent, Tool
from pydantic_ai.models.groq import GroqModel
from pydantic_ai.providers.groq import GroqProvider
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from src.integrations.tool_module import ToolManager
from src.integrations.langfuse import LangfuseIntegration
import pathlib
from dotenv import load_dotenv
import os
import json
import asyncio
from loguru import logger
from pydantic_ai.mcp import MCPServerHTTP

load_dotenv()

# Setup logging
LOG_DIR = os.path.join(os.path.dirname(__file__), '../logs/agent')
os.makedirs(LOG_DIR, exist_ok=True)
logger.add(os.path.join(LOG_DIR, 'info.log'), level='INFO', rotation='10 MB', retention='10 days')
logger.add(os.path.join(LOG_DIR, 'debug.log'), level='DEBUG', rotation='10 MB', retention='10 days')

PARENT_DIR = pathlib.Path(__file__).parent
PROVIDER_FILE = PARENT_DIR / "providers.json"
INTEGRATIONS_FILE = PARENT_DIR / "config" / "integrations.json"


class AgentModule:
    """
    models received will have be in this template <provider>/<model_name>
    Example:
        groq/llama-3.3-70b-versatile
        openrouter/anthropic/claude-3.5-sonnet
    
    Note: get_agent is now synchronous.
    """
    def __init__(self, model_id: str, model_settings: dict = None) -> None:
        self.model_id = model_id  # e.g. 'groq/llama-3.3-70b-versatile'
        provider_parts = model_id.split('/', 1)
        if len(provider_parts) != 2:
            raise ValueError(f"Invalid model_id format: {model_id}. Expected format: provider/model_name")
            
        self.provider, self.model_name = provider_parts
        
        try:
            with open(PROVIDER_FILE, 'r') as f:
                self.providers = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"Failed to load providers file: {e}")
            raise ValueError(f"Failed to load providers from {PROVIDER_FILE}: {e}")
            
        self.provider_info = self.providers.get(self.provider)
        if not self.provider_info:
            raise ValueError(f"Unknown provider: {self.provider}")
            
        self.base_url = self.provider_info.get('base_url')
        if not self.base_url:
            raise ValueError(f"Missing base_url for provider: {self.provider}")
            
        # API key env var convention: <PROVIDER>_API_KEY (uppercased)
        logger.info(f"Getting from env var : {self.provider.upper()}_API_KEY")
        logger.info(f"API Key: {os.getenv(f'{self.provider.upper()}_API_KEY')[-7:]}")
        self.api_key = os.getenv(f"{self.provider.upper()}_API_KEY")
        if not self.api_key:
            raise ValueError(f"API key for {self.provider} not found in environment.")
            
        self.model_settings = model_settings or {}
        
        # Initialize model
        self.model = self._create_model()
        
        # Cached agent instance
        self.agent = None

        self.langfuse_integration = self._get_langfuse_integration()
        # Wrap tool methods with Langfuse tracing if enabled
        if self.langfuse_integration:
            self.retrieve_documents = self.langfuse_integration.trace_tool_call("retrieve_documents")(self.retrieve_documents)
            self.retrieve_feedback = self.langfuse_integration.trace_tool_call("retrieve_feedback")(self.retrieve_feedback)

    def retrieve_documents(self, query: str, match_count: int = 5) -> str:
        """Retrieve relevant documentation from the documents_768 table via Supabase RPC.

        Args:
            query: The user's question or query.
            match_count: Number of matches to return (default: 5).

        Returns:
            Formatted string containing the most relevant documentation chunks, or an error message.
        """
        tool_manager = ToolManager()
        return tool_manager.retrieve_documents(query, match_count)

    def retrieve_feedback(self, query: str, match_count: int = 5) -> str:
        """Retrieve relevant user feedback from the feedback_768 table via Supabase RPC.

        Args:
            query: The user's question or query.
            match_count: Number of matches to return (default: 5).

        Returns:
            Formatted string containing the most relevant feedback entries, or an error message.
        """
        tool_manager = ToolManager()
        return tool_manager.retrieve_feedback(query, match_count)

    def _create_model(self):
        """Create and configure the model based on provider configuration.
        
        Returns:
            Initialized model instance.
            
        Raises:
            ValueError: If provider info is missing or incorrect.
            NotImplementedError: If the provider is not supported.
        """
        if self.provider == 'groq':
            return GroqModel(
                self.model_name,
                provider=GroqProvider(api_key=self.api_key)
            )
        elif self.provider == 'openrouter':
            return OpenAIModel(
                self.model_name,
                provider=OpenAIProvider(base_url=self.base_url, api_key=self.api_key)
            )
        else:
            raise NotImplementedError(f"Provider {self.provider} not supported.")

    def _get_integration_status(self):
        """Get integration status from integrations.json.
        
        Returns:
            Dictionary with integration status.
        """
        if not INTEGRATIONS_FILE.exists():
            logger.warning(f"Integrations file not found: {INTEGRATIONS_FILE}")
            return {
                "tools": {"status": False, "error": "Integrations file not found"},
                "mcp_servers": [],
                "langfuse": {"status": False, "error": "Integrations file not found"}
            }
            
        try:
            with open(INTEGRATIONS_FILE, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load integrations file: {e}")
            return {
                "tools": {"status": False, "error": f"Failed to load integrations file: {e}"},
                "mcp_servers": [],
                "langfuse": {"status": False, "error": f"Failed to load integrations file: {e}"}
            }

    def _get_tools(self):
        """Get available tools based on integration status.
        
        Returns:
            List of tool instances.
        """
        tools = []
        integration_status = self._get_integration_status()
        tool_status = integration_status.get('tools', {}).get('status', False)
        tool_manager = ToolManager()
        if tool_status:
            # Register tools using Tool class for better control
            tools = [
                Tool(self.retrieve_documents, takes_ctx=False),
                Tool(self.retrieve_feedback, takes_ctx=False),
                Tool(tool_manager.get_rule_id_from_user_query, takes_ctx=False)
            ]
            logger.info(f"Initialized {len(tools)} tools for agent")
        else:
            tool_error = integration_status.get('tools', {}).get('error')
            logger.warning(f"Tools not available: {tool_error}")
        return tools

    def _get_mcp_servers(self):
        """Get available MCP servers based on integration status.
        
        Returns:
            List of MCPServerHTTP instances.
        """
        mcp_servers = []
        integration_status = self._get_integration_status()
        mcp_server_statuses = integration_status.get('mcp_servers', [])
        
        for server_status in mcp_server_statuses:
            if server_status.get('status', False):
                url = server_status.get('url')
                if url:
                    mcp_servers.append(MCPServerHTTP(
                        url=url,
                        timeout=10,  # Default timeout in seconds
                        sse_read_timeout=300  # Default SSE read timeout in seconds
                    ))
        
        logger.info(f"Found {len(mcp_servers)} available MCP servers")
        return mcp_servers

    def _get_langfuse_integration(self):
        integration_status = self._get_integration_status()
        langfuse_status = integration_status.get('langfuse', {}).get('status', False)
        if langfuse_status:
            config = {
                "DEBUG": os.getenv("DEBUG", "0") == "1",
                "LANGFUSE_public_key": os.getenv("LANGFUSE_public_key"),
                "LANGFUSE_secret_key": os.getenv("LANGFUSE_secret_key"),
            }
            return LangfuseIntegration(config)
        return None

    def get_agent(self) -> Agent:
        """Create and configure an Agent with the specified model and tools.
        
        This method is now synchronous.
        
        Returns:
            An initialized Agent instance ready for interaction.
        """
        if self.agent:
            return self.agent
            
        # Get tools and MCP servers from integration status
        tools = self._get_tools()
        mcp_servers = self._get_mcp_servers()

        logger.info(f"Tools: {tools}")
        logger.info(f"MCP servers: {mcp_servers}")
        
        # Create the agent
        self.agent = Agent(
            self.model,
            model_settings=self.model_settings,
            mcp_servers=mcp_servers,
            tools=tools,
            instrument=True
        )

        return self.agent
    
if __name__ == "__main__":
    async def main():
        try:
            # Initialize Groq agent
            groq_agent_module = AgentModule("groq/llama-3.3-70b-versatile")
            groq_agent = groq_agent_module.get_agent()
            
            # Test groq agent with MCP servers properly managed
            async with groq_agent.run_mcp_servers():
                result = await groq_agent.run('What is the capital of France?')
                print(f"Groq agent response: {result.output}")
            
            # Initialize OpenRouter agent
            or_agent_module = AgentModule("openrouter/anthropic/claude-3.5-sonnet")
            or_agent = or_agent_module.get_agent()
            
            # Test OpenRouter agent with streaming and proper MCP context
            print("OpenRouter agent response (streaming):")
            async with or_agent.run_mcp_servers():
                async with or_agent.run_stream('What is the capital of the UK?') as response:
                    print(await response.get_output())
        except Exception as e:
            logger.error(f"Error in main: {e}")
            raise

    asyncio.run(main())