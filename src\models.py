from typing import Literal
from typing_extensions import TypedDict
from pydantic import BaseModel

class ChatMessage(TypedDict):
    """Format of messages sent to the browser."""

    role: Literal['user', 'model']
    timestamp: str
    content: str

class RequestModel(BaseModel):
    user_id: str
    session_id: str
    prompt: str
    model: str
    temperature: float = 0.2
    enable_memory: bool = True
