<script lang="ts">
 import { onMount } from 'svelte';
 import { tweened } from 'svelte/motion';
 import { cubicInOut } from 'svelte/easing';
 import { cn } from '$lib/utils';

 let { className } = $props<{ className?: string }>();

 // Motion Instance 1 (Right Conic)
 let motion1Ref = $state<HTMLDivElement | null>(null);
 const opacity1 = tweened(0.8, { duration: 1000, delay: 100, easing: cubicInOut });
 const width1 = tweened(25, { duration: 1000, delay: 100, easing: cubicInOut }); // in rem

 // Motion Instance 2 (Left Conic)
 let motion2Ref = $state<HTMLDivElement | null>(null);
 const opacity2 = tweened(0.8, { duration: 1000, delay: 100, easing: cubicInOut });
 const width2 = tweened(25, { duration: 1000, delay: 100, easing: cubicInOut }); // in rem

 // Motion Instance 3 (Blurry Oval)
 let motion3Ref = $state<HTMLDivElement | null>(null);
 const width3 = tweened(20, { duration: 1000, delay: 100, easing: cubicInOut }); // in rem

 // Motion Instance 4 (Horizontal Line)
 let motion4Ref = $state<HTMLDivElement | null>(null);
 const width4 = tweened(25, { duration: 1000, delay: 100, easing: cubicInOut }); // in rem

 onMount(() => {
 	// Start animations immediately for background effect with more subtle visibility
 	opacity1.set(0.4);
 	width1.set(35);
 	opacity2.set(0.4);
 	width2.set(35);
 	width3.set(30);
 	width4.set(35);
 });
</script>

<!-- Fixed positioned background lamp effect -->
<div
	class={cn(
		'fixed inset-0 w-screen h-screen overflow-hidden pointer-events-none',
		className
	)}
	style="z-index: 0;"
>
 <!-- Semi-circular lamp effect emanating from top -->
 <div class="absolute top-0 left-1/2 -translate-x-1/2 w-full h-full flex items-start justify-center">

   <!-- Main semi-circular glow -->
   <div
   	class="absolute -top-32 left-1/2 -translate-x-1/2 w-[80rem] h-[40rem] rounded-full bg-gradient-to-b from-cyan-400/20 via-cyan-500/10 to-transparent blur-[100px]"
   ></div>

   <!-- Secondary wider glow for subtle fill -->
   <div
   	class="absolute -top-48 left-1/2 -translate-x-1/2 w-[120rem] h-[60rem] rounded-full bg-gradient-to-b from-cyan-300/8 via-cyan-400/4 to-transparent blur-[150px]"
   ></div>

   <!-- Conic gradients for directional light -->
   <!-- Right Conic Gradient -->
   <div
   	bind:this={motion1Ref}
   	style="opacity: {$opacity1 * 0.6}; width: {$width1}rem; background: conic-gradient(from 70deg at center top, #06b6d4, transparent, transparent);"
   	class="absolute inset-auto right-1/2 h-80 overflow-visible opacity-30"
   ></div>

   <!-- Left Conic Gradient -->
   <div
   	bind:this={motion2Ref}
   	style="opacity: {$opacity2 * 0.6}; width: {$width2}rem; background: conic-gradient(from 290deg at center top, transparent, transparent, #06b6d4);"
   	class="absolute inset-auto left-1/2 h-80 overflow-visible opacity-30"
   ></div>

   <!-- Focused center glow -->
   <div
   	bind:this={motion3Ref}
   	style="width: {$width3}rem;"
   	class="absolute top-8 left-1/2 -translate-x-1/2 h-32 rounded-full bg-cyan-400/15 blur-[80px]"
   ></div>

   <!-- Horizontal accent line -->
   <div
   	bind:this={motion4Ref}
   	style="width: {$width4}rem;"
   	class="absolute top-12 left-1/2 -translate-x-1/2 h-0.5 bg-cyan-400/20 blur-sm"
   ></div>

   <!-- Subtle edge fade masks -->
   <div class="absolute top-0 left-0 w-32 h-full bg-gradient-to-r from-black/20 to-transparent"></div>
   <div class="absolute top-0 right-0 w-32 h-full bg-gradient-to-l from-black/20 to-transparent"></div>
   <div class="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-black/30 to-transparent"></div>
 </div>
</div>