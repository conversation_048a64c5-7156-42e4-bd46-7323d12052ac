<script lang="ts">
 import { onMount } from 'svelte';
 import { tweened } from 'svelte/motion';
 import { cubicInOut } from 'svelte/easing';
 import { cn } from '$lib/utils';

 let { className } = $props<{ className?: string }>();

 // Motion Instance 1 (Right Conic)
 let motion1Ref = $state<HTMLDivElement | null>(null);
 const opacity1 = tweened(0.8, { duration: 1000, delay: 100, easing: cubicInOut });
 const width1 = tweened(25, { duration: 1000, delay: 100, easing: cubicInOut }); // in rem

 // Motion Instance 2 (Left Conic)
 let motion2Ref = $state<HTMLDivElement | null>(null);
 const opacity2 = tweened(0.8, { duration: 1000, delay: 100, easing: cubicInOut });
 const width2 = tweened(25, { duration: 1000, delay: 100, easing: cubicInOut }); // in rem

 // Motion Instance 3 (Blurry Oval)
 let motion3Ref = $state<HTMLDivElement | null>(null);
 const width3 = tweened(20, { duration: 1000, delay: 100, easing: cubicInOut }); // in rem

 // Motion Instance 4 (Horizontal Line)
 let motion4Ref = $state<HTMLDivElement | null>(null);
 const width4 = tweened(25, { duration: 1000, delay: 100, easing: cubicInOut }); // in rem

 onMount(() => {
 	// Start animations immediately for background effect with more subtle visibility
 	opacity1.set(0.4);
 	width1.set(35);
 	opacity2.set(0.4);
 	width2.set(35);
 	width3.set(30);
 	width4.set(35);
 });
</script>

<!-- Fixed positioned background lamp effect -->
<div
	class={cn(
		'fixed inset-0 w-screen h-screen flex items-start justify-center overflow-visible pointer-events-none',
		className
	)}
	style="z-index: 0;"
>
 <!-- Right Conic Gradient -->
 <div
 	bind:this={motion1Ref}
 	style="opacity: {$opacity1}; width: {$width1}rem; background: conic-gradient(from 70deg at center top, #06b6d4, transparent, transparent);"
 	class="absolute inset-auto right-1/2 h-80 overflow-visible"
 ></div>

 <!-- Left Conic Gradient -->
 <div
 	bind:this={motion2Ref}
 	style="opacity: {$opacity2}; width: {$width2}rem; background: conic-gradient(from 290deg at center top, transparent, transparent, #06b6d4);"
 	class="absolute inset-auto left-1/2 h-80 overflow-visible"
 ></div>

 <!-- Large Background Blur -->
 <div
 	class="absolute inset-auto h-96 w-[100rem] -translate-y-1/4 rounded-full bg-cyan-500 opacity-20 blur-[200px]"
 ></div>

 <!-- Medium Blur Oval -->
 <div
 	bind:this={motion3Ref}
 	style="width: {$width3}rem;"
 	class="absolute inset-auto h-64 -translate-y-[3rem] rounded-full bg-cyan-400 opacity-30 blur-[100px]"
 ></div>

 <!-- Horizontal Line -->
 <div
 	bind:this={motion4Ref}
 	style="width: {$width4}rem;"
 	class="absolute inset-auto h-1 -translate-y-[4rem] bg-cyan-400 opacity-40"
 ></div>
</div>