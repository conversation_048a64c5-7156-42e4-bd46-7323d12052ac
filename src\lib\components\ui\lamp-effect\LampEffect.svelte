    <script lang="ts">
	import { onMount } from 'svelte';
	import { tweened } from 'svelte/motion';
	import { cubicInOut } from 'svelte/easing';
	import { cn } from '$lib/utils';

	let { className } = $props<{ className?: string }>();

	// Motion Instance 1 (Right Conic)
	let motion1Ref = $state<HTMLDivElement | null>(null);
	const opacity1 = tweened(0.5, { duration: 800, delay: 300, easing: cubicInOut });
	const width1 = tweened(15, { duration: 800, delay: 300, easing: cubicInOut }); // in rem

	// Motion Instance 2 (Left Conic)
	let motion2Ref = $state<HTMLDivElement | null>(null);
	const opacity2 = tweened(0.5, { duration: 800, delay: 300, easing: cubicInOut });
	const width2 = tweened(15, { duration: 800, delay: 300, easing: cubicInOut }); // in rem

	// Motion Instance 3 (Blurry Oval)
	let motion3Ref = $state<HTMLDivElement | null>(null);
	const width3 = tweened(8, { duration: 800, delay: 300, easing: cubicInOut }); // in rem

	// Motion Instance 4 (Horizontal Line)
	let motion4Ref = $state<HTMLDivElement | null>(null);
	const width4 = tweened(15, { duration: 800, delay: 300, easing: cubicInOut }); // in rem

	onMount(() => {
		const observerOptions = {
			root: null,
			rootMargin: '0px',
			threshold: 0.1 // Trigger when 10% of the element is visible
		};

		const observerCallback = (entries: IntersectionObserverEntry[]) => {
			entries.forEach((entry) => {
				if (entry.isIntersecting) {
					if (entry.target === motion1Ref) {
						opacity1.set(1);
						width1.set(30);
					} else if (entry.target === motion2Ref) {
						opacity2.set(1);
						width2.set(30);
					} else if (entry.target === motion3Ref) {
						width3.set(16);
					} else if (entry.target === motion4Ref) {
						width4.set(30);
					}
				}
			});
		};

		const observer = new IntersectionObserver(observerCallback, observerOptions);

		if (motion1Ref) observer.observe(motion1Ref);
		if (motion2Ref) observer.observe(motion2Ref);
		if (motion3Ref) observer.observe(motion3Ref);
		if (motion4Ref) observer.observe(motion4Ref);

		return () => {
			observer.disconnect();
		};
	});
</script>

<div
	class={cn(
		'relative z-0 flex min-h-screen w-full flex-col items-center justify-center overflow-hidden rounded-md bg-slate-950',
		className
	)}
>
	<div class="relative isolate z-0 flex w-full flex-1 scale-y-125 items-center justify-center">
		&lt;!-- Motion Instance 1 (Right Conic) --&gt;
		<div
			bind:this={motion1Ref}
			style="opacity: {$opacity1}; width: {$width1}rem; background-image: conic-gradient(var(--conic-position), var(--tw-gradient-stops));"
			class="bg-gradient-conic absolute inset-auto right-1/2 h-56 overflow-visible from-cyan-500 via-transparent to-transparent text-white [--conic-position:from_70deg_at_center_top]"
		>
			<div
				class="absolute bottom-0 left-0 z-20 h-40 w-[100%] bg-slate-950 [mask-image:linear-gradient(to_top,white,transparent)]"
			/>
			<div
				class="absolute bottom-0 left-0 z-20 h-[100%] w-40 bg-slate-950 [mask-image:linear-gradient(to_right,white,transparent)]"
			/>
		</div>

		&lt;!-- Motion Instance 2 (Left Conic) --&gt;
		<div
			bind:this={motion2Ref}
			style="opacity: {$opacity2}; width: {$width2}rem; background-image: conic-gradient(var(--conic-position), var(--tw-gradient-stops));"
			class="bg-gradient-conic absolute inset-auto left-1/2 h-56 from-transparent via-transparent to-cyan-500 text-white [--conic-position:from_290deg_at_center_top]"
		>
			<div
				class="absolute bottom-0 right-0 z-20 h-[100%] w-40 bg-slate-950 [mask-image:linear-gradient(to_left,white,transparent)]"
			/>
			<div
				class="absolute bottom-0 right-0 z-20 h-40 w-[100%] bg-slate-950 [mask-image:linear-gradient(to_top,white,transparent)]"
			/>
		</div>

		&lt;!-- Static elements - no animation needed --&gt;
		<div
			class="absolute top-1/2 h-48 w-full translate-y-12 scale-x-150 bg-slate-950 blur-2xl"
		></div>
		<div class="absolute top-1/2 z-50 h-48 w-full bg-transparent opacity-10 backdrop-blur-md"></div>
		<div
			class="absolute inset-auto z-50 h-36 w-[28rem] -translate-y-1/2 rounded-full bg-cyan-500 opacity-50 blur-3xl"
		></div>

		&lt;!-- Motion Instance 3 (Blurry Oval) --&gt;
		<div
			bind:this={motion3Ref}
			style="width: {$width3}rem;"
			class="absolute inset-auto z-30 h-36 -translate-y-[6rem] rounded-full bg-cyan-400 blur-2xl"
		></div>

		&lt;!-- Motion Instance 4 (Horizontal Line) --&gt;
		<div
			bind:this={motion4Ref}
			style="width: {$width4}rem;"
			class="absolute inset-auto z-50 h-0.5 -translate-y-[7rem] bg-cyan-400"
		></div>

		<div class="absolute inset-auto z-40 h-44 w-full -translate-y-[12.5rem] bg-slate-950"></div>
	</div>

	<div class="relative z-50 flex -translate-y-80 flex-col items-center px-5">
		<slot />
	</div>
</div>