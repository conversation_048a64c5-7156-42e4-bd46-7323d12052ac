### `/quick_stat` endpoint
Implement vehicle quick stat endpoint to get this data into context.
Brief PLAN is to implement the this within @tool_module.py so that this can be used as both:
- As a tool to the agent
- As a direct endpoint available @core.py fastapi

### N8N Node Details
```json
{
  "nodes": [
    {
      "parameters": {
        "url": "=https://voltron-8080a.corp.olaelectric.com/api/v1/vehicle/{{ $json.body.vin_number }}",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "<PERSON>ie",
              "value": "OSRN_v1=r58f3i3w65C_hceO9-w9Zpzw"
            },
            {
              "name": "cookie",
              "value": "OSRN_v1=r58f3i3w65C_hceO9-w9Zpzw;"
            }
          ]
        },
        "options": {
          "redirect": {
            "redirect": {}
          }
        }
      },
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        480,
        260
      ],
      "id": "2faa78fd-7bb7-4b8f-aa5f-79c2047abf7d",
      "name": "Voltron Vehicle Quick"
    }
  ],
  "connections": {
    "Voltron Vehicle Quick": {
      "main": [
        []
      ]
    }
  },
  "pinData": {},
  "meta": {
    "instanceId": "87f543696fbbc9747c32cfec9522418972a08ad56edb74442a02423716dbf09d"
  }
}
```

### Response Received
```json
[
  {
    "status": "SUCCESS",
    "data": {
      "vehicleId": "P53AWDCC2CHA00047",
      "totalDistanceCovered": {
        "value": "45852",
        "updatedAt": "1747927829481"
      },
      "vehicleStatus": {
        "value": "OFF",
        "updatedAt": "1747927828545"
      },
      "chargingStatus": {
        "value": "NOT_CHARGING",
        "updatedAt": "1747927829481"
      },
      "location": {
        "value": "12.876951,77.644966",
        "latitude": "12.876951",
        "longitude": "77.644966",
        "network_latitude": "0.0",
        "network_longitude": "0.0",
        "horizontal_accuracy": "2.6",
        "vertical_accuracy": "18.4",
        "updatedAt": "1747927829491"
      },
      "range": {
        "rangeAi": "93",
        "rangeSportAi": "84",
        "rangeHyperAi": "69",
        "rangeEcoAi": "113",
        "rangeCustomAi": "113",
        "updatedAt": "1747927829481",
        "availableDrivingMode": 0
      },
      "averageChargeInfo": {
        "batteryCharge": "57.98",
        "timeToCharge": "198.0",
        "timeTo80Charge": "23.0",
        "updatedAt": "1747927829481"
      },
      "scooterMode": {
        "value": "3",
        "updatedAt": "1747749971619"
      },
      "driveMode": {
        "value": "PARKED",
        "updatedAt": "1747749971619"
      },
      "otaStatus": {
        "otaStatus": "OTA_STATUS_INSTALL_SUCCESS",
        "vehicleSoftwareVersion": "5.6.0.gen3.0413",
        "updatedAt": "1747749931234"
      },
      "trackingTime": {
        "value": "dp.hmi.quectel.gps.data.packet.v2",
        "updatedAt": "1747927829491"
      },
      "lockStatus": {
        "value": "PARTIAL_LOCK",
        "updatedAt": "1747385139566"
      },
      "trunkStatus": {
        "value": "ON",
        "updatedAt": "1745661309516"
      },
      "sideStandStatus": {
        "value": "OFF",
        "updatedAt": "1747749973289"
      },
      "vehicleVariant": {
        "value": "26",
        "updatedAt": "1747749931234",
        "finalVariant": "26"
      },
      "sleepState": {
        "value": "SLEEP",
        "updatedAt": "1747927828534"
      },
      "vacationMode": {
        "value": "OFF",
        "updatedAt": "1747927828534"
      },
      "vehicleAddonFeatures": {},
      "modifiedAt": "2025-05-22T15:55:19.057+00:00",
      "vehicleSettings": {
        "mapsEnabledSettings": {
          "value": "",
          "updatedAt": ""
        },
        "btStatusSettings": {
          "value": "OFF",
          "updatedAt": "1747927818959"
        },
        "hcWhitelistSettings": {
          "value": "",
          "updatedAt": ""
        }
      },
      "vehicleSafetyData": {
        "fallDetection": {
          "vehicleFallDetected": "OFF",
          "updatedAt": "1738416623692"
        },
        "tamperedDetection": {
          "vehicleTampered": "",
          "updatedAt": ""
        },
        "accidentDetection": {
          "vehicleAccidentDetected": "",
          "updatedAt": ""
        }
      },
      "faultState": {},
      "vehicleAddonMetadata": {},
      "regenStatus": {},
      "serviceMode": {},
      "fallDetectionEverEnabled": {
        "updatedAt": "1747749904132",
        "value": "OFF"
      },
      "customDriveModeDetails": {
        "name": "MYMODE",
        "toggleStatus": "OFF",
        "updatedAt": "1744691693691"
      }
    }
  }
]
```