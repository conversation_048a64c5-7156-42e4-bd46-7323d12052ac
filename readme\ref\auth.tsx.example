"use client";

import { useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";

// UI Components
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";

// Auth Context
import { useAuth } from "~/contexts/AuthContext";

const domains = [
  { value: "olaelectric.com", label: "@olaelectric.com" },
  { value: "olacabs.com", label: "@olacabs.com" },
  { value: "olakrutrim.com", label: "@olakrutrim.com" },
];

export default function AuthPage() {
  const [isLogin, setIsLogin] = useState(true);
  const [emailPrefix, setEmailPrefix] = useState("");
  const [domain, setDomain] = useState("olaelectric.com");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  
  const { login, signup, error } = useAuth();
  const router = useRouter();

  const getFullEmail = () => `${emailPrefix}@${domain}`;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      if (isLogin) {
        await login(getFullEmail(), password);
        router.push("/chat");
      } else {
        await signup(getFullEmail(), password);
        // Stay on the page to notify about verification email
      }
    } catch (error) {
      console.error("Authentication error:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen">
      {/* Left side with image and animation */}
      <div className="hidden md:flex md:w-1/2 bg-orange-50 items-center justify-center relative">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
          className="relative w-4/5 h-4/5"
        >
          <Image
            src="/img/vs_anime.png"
            alt="Vahan Sahayak"
            fill
            style={{ objectFit: "contain" }}
            priority
          />
        </motion.div>
      </div>
      
      {/* Right side with auth form */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-8">
        <div className="max-w-md w-full">
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold mb-2">
              {isLogin ? "Welcome Back" : "Create an Account"}
            </h1>
            <p className="text-gray-600">
              {isLogin 
                ? "Sign in to access Vahan Sahayak" 
                : "Sign up to get started with Vahan Sahayak"}
            </p>
          </div>
          
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-3 mb-4">
              {error}
            </div>
          )}
          
          {!isLogin && (
            <div className="bg-blue-50 border border-blue-200 text-blue-800 rounded-md p-3 mb-4">
              You will receive a verification email after sign up.
            </div>
          )}
          
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">Email</label>
              <div className="flex">
                <Input 
                  value={emailPrefix}
                  onChange={(e) => setEmailPrefix(e.target.value)}
                  placeholder="username"
                  required
                  disabled={loading}
                  className="rounded-r-none"
                />
                <Select 
                  value={domain} 
                  onValueChange={setDomain}
                  disabled={loading}
                >
                  <SelectTrigger className="w-[180px] rounded-l-none">
                    <SelectValue placeholder="@domain.com" />
                  </SelectTrigger>
                  <SelectContent>
                    {domains.map((domain) => (
                      <SelectItem key={domain.value} value={domain.value}>
                        {domain.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="mb-6">
              <label className="block text-sm font-medium mb-1">Password</label>
              <Input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="••••••••"
                required
                disabled={loading}
              />
            </div>
            
            <Button 
              type="submit" 
              className="w-full"
              disabled={loading}
            >
              {loading ? "Processing..." : isLogin ? "Sign In" : "Sign Up"}
            </Button>
          </form>
          
          <div className="mt-6 text-center">
            <button
              onClick={() => setIsLogin(!isLogin)}
              className="text-blue-600 hover:underline text-sm"
              disabled={loading}
            >
              {isLogin 
                ? "Don't have an account? Sign up" 
                : "Already have an account? Sign in"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 