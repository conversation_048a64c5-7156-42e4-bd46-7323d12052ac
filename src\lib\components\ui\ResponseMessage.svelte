<script lang="ts">
  import { marked } from 'marked';

  export let content: string;
  export let timestamp: string;
  export let model: string;

  let parsedContent: string;

  // Parse markdown content
  $: {
    try {
      // Use marked.parse synchronously
      parsedContent = marked.parse(content, { async: false }) as string;
    } catch (error) {
      console.error('Error parsing markdown:', error);
      parsedContent = content; // Fallback to raw content if parsing fails
    }
  }
</script>

<div class="flex justify-start items-start gap-2 w-full">
  <img src="https://api.dicebear.com/7.x/shapes/svg?seed={model}" alt="model avatar" class="rounded-full w-10 h-10 border flex-shrink-0" />
  <div class="flex flex-col items-start max-w-full">
    <div class="bg-muted text-foreground rounded-xl px-4 py-2 max-w-[70vw] shadow overflow-hidden">
      <span class="font-semibold text-sm">{model}</span>
      <div class="text-base prose prose-sm dark:prose-invert overflow-auto max-w-full">
        <div class="overflow-x-auto w-full">
          {@html parsedContent}
        </div>
      </div>
      <span class="text-xs text-muted-foreground">{new Date(timestamp).toLocaleTimeString()}</span>
    </div>
  </div>
</div>

<style>
  /* Ensure code blocks don't overflow */
  :global(.prose pre) {
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-width: 100%;
  }

  /* Ensure tables don't overflow */
  :global(.prose table) {
    display: block;
    overflow-x: auto;
    max-width: 100%;
  }

  /* Ensure images don't overflow */
  :global(.prose img) {
    max-width: 100%;
    height: auto;
  }

  /* Ensure long words break */
  :global(.prose p, .prose li) {
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
  }
</style>