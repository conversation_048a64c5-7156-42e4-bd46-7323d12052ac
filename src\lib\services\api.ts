import { logger } from '$lib/utils';

// Load environment variables
let FASTAPI_HOST: string;
let FASTAPI_PORT: string;

// In browser environment, we need to load the env variables from the server
// This will be populated during initialization
let API_BASE_URL: string = '';

// Status types for validation
export type ValidationStatus = 'secondary' | 'default' | 'destructive' | 'outline';

// Interface for validation results
export interface ValidationResult {
  status: ValidationStatus;
  message: string;
}

/**
 * Initialize the API service with environment variables
 * This should be called before making any API requests
 */
export async function initApiService() {
  try {
    const response = await fetch('/api/env');
    if (!response.ok) {
      throw new Error(`Failed to load environment variables: ${response.status}`);
    }
    const data = await response.json();
    API_BASE_URL = data.API_BASE_URL;
    logger.info({ event: 'api_service_initialized', base_url: API_BASE_URL });
  } catch (error) {
    logger.error({ event: 'api_service_init_error', error });
    // Fallback to default values
    API_BASE_URL = 'http://localhost:8000';
    logger.info({ event: 'api_service_using_fallback', base_url: API_BASE_URL });
  }
}

/**
 * Send a chat request to the FastAPI server
 */
export async function sendChatRequest(payload: {
  prompt: string;
  model: string;
  temperature: number;
  user_id: string;
  session_id: string;
  enable_memory: boolean;
  vin?: string;
  phone?: string;
  order_id?: string;
}) {
  try {
    // Ensure API service is initialized
    if (!API_BASE_URL) {
      await initApiService();
    }

    const response = await fetch(`${API_BASE_URL}/chat/${encodeURIComponent(payload.session_id)}/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API request failed: ${response.status} - ${errorText}`);
    }

    // Handle streaming response properly
    if (!response.body) {
      throw new Error('Response body is null');
    }

    // Process the streaming response
    const reader = response.body.getReader();
    let responseText = '';
    const decoder = new TextDecoder();
    let messages: any[] = [];

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        // Decode the chunk and add it to the accumulated text
        const chunk = decoder.decode(value, { stream: true });
        responseText += chunk;

        // Process any complete lines in the accumulated text
        const lines = responseText.split('\n');
        // Keep the last line (which might be incomplete) for the next iteration
        responseText = lines.pop() || '';

        // Process complete lines
        for (const line of lines) {
          if (line.trim().length > 0) {
            try {
              const message = JSON.parse(line);
              messages.push(message);
            } catch (e) {
              logger.error({
                event: 'chat_response_parse_error',
                error: e,
                line
              });
            }
          }
        }
      }

      // Process any remaining text
      if (responseText.trim().length > 0) {
        try {
          const message = JSON.parse(responseText);
          messages.push(message);
        } catch (e) {
          logger.error({
            event: 'chat_response_parse_error',
            error: e,
            responseText
          });
        }
      }
    } catch (streamError) {
      logger.error({
        event: 'chat_stream_error',
        error: streamError,
        message: (streamError as Error).message,
        partialResponse: responseText
      });
      throw new Error(`Stream error: ${(streamError as Error).message}`);
    }

    logger.info({
      event: 'chat_response_messages',
      messageCount: messages.length,
      messages
    });

    // If we have messages, use the response format expected by the client
    if (messages.length > 0) {
      // Get the last message as the main response
      const lastMessage = messages[messages.length - 1];
      return {
        response: lastMessage.content,
        messages: messages
      };
    } else {
      throw new Error('No valid messages found in response');
    }
  } catch (error) {
    logger.error({ event: 'chat_request_error', error });
    throw error;
  }
}

/**
 * Fetch chat history for a session
 */
export async function fetchChatHistory(sessionId: string) {
  try {
    // Ensure API service is initialized
    if (!API_BASE_URL) {
      await initApiService();
    }

    logger.info({ event: 'fetch_chat_history', sessionId, url: `${API_BASE_URL}/chat/${encodeURIComponent(sessionId)}/` });

    const response = await fetch(`${API_BASE_URL}/chat/${encodeURIComponent(sessionId)}/`);

    if (!response.ok) {
      throw new Error(`Failed to fetch chat history: ${response.status}`);
    }

    // Handle streaming response properly
    if (!response.body) {
      throw new Error('Response body is null');
    }

    // Process the streaming response
    const reader = response.body.getReader();
    let responseText = '';
    const decoder = new TextDecoder();
    let messages: any[] = [];

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        // Decode the chunk and add it to the accumulated text
        const chunk = decoder.decode(value, { stream: true });
        responseText += chunk;

        // Process any complete lines in the accumulated text
        const lines = responseText.split('\n');
        // Keep the last line (which might be incomplete) for the next iteration
        responseText = lines.pop() || '';

        // Process complete lines
        for (const line of lines) {
          if (line.trim().length > 0) {
            try {
              const message = JSON.parse(line);
              messages.push(message);
            } catch (e) {
              logger.error({
                event: 'chat_history_parse_error',
                error: e,
                line
              });
            }
          }
        }
      }

      // Process any remaining text
      if (responseText.trim().length > 0) {
        try {
          const message = JSON.parse(responseText);
          messages.push(message);
        } catch (e) {
          logger.error({
            event: 'chat_history_parse_error',
            error: e,
            responseText
          });
        }
      }
    } catch (streamError) {
      logger.error({
        event: 'chat_history_stream_error',
        error: streamError,
        message: (streamError as Error).message
      });
      throw new Error(`Stream error: ${(streamError as Error).message}`);
    }

    logger.info({
      event: 'chat_history_received',
      messageCount: messages.length,
      messages
    });

    return { messages };
  } catch (error) {
    logger.error({ event: 'fetch_chat_history_failed', error });
    throw error;
  }
}

/**
 * Validate a VIN number
 * @param vin The VIN number to validate
 * @returns Validation result with status and message
 */
export async function validateVIN(vin: string): Promise<ValidationResult> {
  // For now, we'll do a simple validation
  // In a real implementation, this would call an API endpoint

  if (!vin) {
    return { status: 'secondary', message: 'No VIN provided' };
  }

  // Basic VIN validation (17 characters, alphanumeric except I, O, Q)
  const vinRegex = /^[A-HJ-NPR-Z0-9]{17}$/;

  if (!vinRegex.test(vin)) {
    return {
      status: 'destructive',
      message: 'Invalid VIN format. VIN should be 17 characters (no I, O, or Q)'
    };
  }

  // Simulate API call to check if VIN exists in database
  try {
    // In a real implementation, this would be an actual API call
    // await fetch(`${API_BASE_URL}/validate/vin/${encodeURIComponent(vin)}`);

    // For demo purposes, we'll just return success for valid format
    return { status: 'default', message: 'VIN validated' };
  } catch (error) {
    return { status: 'destructive', message: 'Error validating VIN' };
  }
}

/**
 * Validate a phone number
 * @param phone The phone number to validate
 * @returns Validation result with status and message
 */
export async function validatePhone(phone: string): Promise<ValidationResult> {
  if (!phone) {
    return { status: 'secondary', message: 'No phone number provided' };
  }

  // Basic phone validation (10 digits, optionally with formatting)
  const phoneRegex = /^\+?[0-9]{10,15}$/;
  const digitsOnly = phone.replace(/\D/g, '');

  if (!phoneRegex.test(digitsOnly)) {
    return {
      status: 'destructive',
      message: 'Invalid phone format. Should be 10-15 digits'
    };
  }

  // Simulate API call to check if phone exists in database
  try {
    // In a real implementation, this would be an actual API call
    // await fetch(`${API_BASE_URL}/validate/phone/${encodeURIComponent(digitsOnly)}`);

    // For demo purposes, we'll just return success for valid format
    return { status: 'default', message: 'Phone number validated' };
  } catch (error) {
    return { status: 'destructive', message: 'Error validating phone number' };
  }
}

/**
 * Validate an order ID
 * @param orderId The order ID to validate
 * @returns Validation result with status and message
 */
export async function validateOrderId(orderId: string): Promise<ValidationResult> {
  if (!orderId) {
    return { status: 'secondary', message: 'No order ID provided' };
  }

  // Basic order ID validation (alphanumeric, at least 5 characters)
  const orderIdRegex = /^[A-Za-z0-9-]{5,}$/;

  if (!orderIdRegex.test(orderId)) {
    return {
      status: 'destructive',
      message: 'Invalid order ID format. Should be at least 5 alphanumeric characters'
    };
  }

  // Simulate API call to check if order ID exists in database
  try {
    // In a real implementation, this would be an actual API call
    // await fetch(`${API_BASE_URL}/validate/order/${encodeURIComponent(orderId)}`);

    // For demo purposes, we'll just return success for valid format
    return { status: 'default', message: 'Order ID validated' };
  } catch (error) {
    return { status: 'destructive', message: 'Error validating order ID' };
  }
}
