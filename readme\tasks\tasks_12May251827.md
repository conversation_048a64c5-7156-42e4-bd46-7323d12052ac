# Tasks: Move High-Level Langfuse Tracing to core.py (12 May 25 18:27)

- [ ] **Remove high-level agent input/output tracing from agent.py**
    - Ensure run and run_stream are no longer wrapped with Langfuse tracing in agent.py.

- [ ] **Add Langfuse tracing to /chat/{session_id}/ endpoint in core.py**
    - Initialize LangfuseIntegration and get a tracer.
    - Start a Langfuse span at the beginning of the endpoint or stream_messages function.
    - **Set recommended Langfuse attributes on the span:**
        - `langfuse.user.id` (user_id)
        - `langfuse.session.id` (session_id)
        - `langfuse.tags` (e.g., ["chat", "mcp"])
        - `input.prompt` (user_query)
        - Any other relevant metadata (model, etc.)
    - Enter the MCP context manager and agent.run_stream context manager inside the span.
    - **After the agent call, set output attributes on the span:**
        - `output.llm_response` (LLM response text)
    - Yield/return the response as usual.

- [ ] **Ensure tool-level tracing remains in agent.py**
    - Keep tracing for retrieve_documents, retrieve_feedback, etc.

- [ ] **Test and validate integration**
    - Confirm that the full session, including MCP context, is traced in Langfuse and recommended attributes are present.
    - Confirm that tool-level traces are still present.
    - Update documentation/readme as needed. 