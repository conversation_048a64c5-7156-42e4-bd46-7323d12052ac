{"name": "vahan-sahay<PERSON>-ui-svelte", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint ."}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@skeletonlabs/skeleton": "^3.1.3", "@skeletonlabs/skeleton-svelte": "^1.2.2", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.0.0", "autoprefixer": "^10.4.20", "bits-ui": "0.22.0", "clsx": "^2.1.1", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "lucide-svelte": "^0.510.0", "mini-svg-data-uri": "^1.4.4", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.10", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwind-merge": "^3.3.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^3.4.17", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.2.6"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}, "dependencies": {"dotenv": "^16.5.0", "firebase": "^11.7.3", "marked": "^15.0.12", "mode-watcher": "0.5.1", "pino": "^9.6.0", "svelte-motion": "^0.12.2", "sveltekit-superforms": "^2.25.0", "zod": "^3.24.4"}}