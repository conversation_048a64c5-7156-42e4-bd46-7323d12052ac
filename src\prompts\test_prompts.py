"""
Test script for the prompts system.
"""
from rich.console import <PERSON>sole
from rich.markdown import Markdown

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from src.prompts import get_formatted_system_prompt

def main():
    """Test the prompts system by displaying the formatted system prompt."""
    console = Console()

    print("=== Testing Prompts System ===")
    print("Displaying the formatted system prompt:")
    print()

    # Get and display the formatted system prompt
    system_prompt = get_formatted_system_prompt()
    console.print(Markdown(system_prompt))

    print("\nTest completed successfully!")

if __name__ == "__main__":
    main()
