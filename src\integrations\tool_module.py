from supabase import create_client
from pydantic_ai import Tool

from google import genai
from google.genai.types import EmbedContentConfig

from loguru import logger

from dotenv import load_dotenv
import os
from typing import Tuple, Optional
load_dotenv()

# Logging setup as per project rules
LOG_DIR = os.path.join(os.path.dirname(__file__), '../../logs/tool_module')
os.makedirs(LOG_DIR, exist_ok=True)
logger.add(os.path.join(LOG_DIR, 'info.log'), level='INFO', rotation='10 MB', retention='10 days')
logger.add(os.path.join(LOG_DIR, 'debug.log'), level='DEBUG', rotation='10 MB', retention='10 days')

class ToolManager:
    def __init__(self):
        self.supabase_url = os.getenv('SUPABASE_DATA_API')
        self.supabase_key = os.getenv('SUPABASE_PSQL_SERVICE_KEY')
        self.supabase_client = create_client(self.supabase_url, self.supabase_key)
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        self.gemini_client = genai.Client(api_key=self.gemini_api_key)

    def check_health(self) -> Tuple[bool, Optional[str]]:
        try:
            logger.debug("Testing Supabase connection with a simple query")
            _ = self.supabase_client.from_('document_rows').select('row_data').limit(1).execute()
            logger.debug("Supabase connection test successful")
            return True, None
        except Exception as e:
            logger.error(f"Error checking health: {e}")
            return False, str(e)

    async def get_embeddings(self, text: str) -> list[float]:
        """Get text embeddings from Gemini API.

        Args:
            text (str): The input text to embed.

        Returns:
            list[float]: The embedding vector for the input text.

        Raises:
            ValueError: If the Gemini API key is not set.
            RuntimeError: If the embedding request fails.
        """
        
        if not self.gemini_api_key:
            logger.error("GEMINI_API_KEY must be set in environment variables")
            raise ValueError("GEMINI_API_KEY must be set in environment variables")

        try:
            gemini_client = genai.Client(api_key=self.gemini_api_key)
            result = gemini_client.models.embed_content(
                model="models/text-embedding-004",
                contents=[text],
                config=EmbedContentConfig(task_type="RETRIEVAL_DOCUMENT")
            )
            embedding = result.embeddings[0].values
            logger.debug(f"Generated embedding for text: {text[:30]}... (len={len(embedding)})")
            return embedding
        except Exception as e:
            logger.error(f"Error creating embedding: {e}")
            # Return a zero vector of length 768 as fallback
            return [0.0] * 768

    async def get_tools(self) -> list[Tool]:
        ...

    def create_embeddings_batch(self, texts: list[str]) -> list[list[float]]:
        """Create embeddings for multiple texts in a single API call using Gemini client.

        Args:
            texts (list[str]): List of texts to create embeddings for.

        Returns:
            list[list[float]]: List of embeddings (each embedding is a list of floats).
        """
        if not texts:
            return []
        try:
            result = self.gemini_client.models.embed_content(
                model="models/text-embedding-004",
                contents=texts,
                config=EmbedContentConfig(task_type="RETRIEVAL_DOCUMENT")
            )
            embeddings = [embedding.values for embedding in result.embeddings]
            logger.debug(f"Generated batch embeddings for {len(texts)} texts.")
            return embeddings
        except Exception as e:
            logger.error(f"Error creating batch embeddings: {e}")
            return [[0.0] * 768 for _ in range(len(texts))]

    def create_embedding(self, text: str) -> list[float]:
        """Create an embedding for a single text using Gemini client.

        Args:
            text (str): Text to create an embedding for.

        Returns:
            list[float]: The embedding vector for the input text.
        """
        try:
            embeddings = self.create_embeddings_batch([text])
            return embeddings[0] if embeddings else [0.0] * 768
        except Exception as e:
            logger.error(f"Error creating embedding: {e}")
            return [0.0] * 768

    def retrieve_documents(self, query: str, match_count: int = 5) -> str:
        """Retrieve relevant documentation from the documents_768 table via Supabase RPC.

        Args:
            query (str): The user's question or query.
            match_count (int): Number of matches to return (default: 5).

        Returns:
            str: Formatted string containing the most relevant documentation chunks, or an error message.
        """
        if not self.supabase_client:
            logger.error("Supabase client not available in ToolManager")
            return "Error: Supabase connection not available."
        try:
            logger.debug(f"Retrieving documents for query: '{query[:50]}...'")
            query_embedding = self.create_embedding(query)
            docs_result = self.supabase_client.rpc(
                "match_documents_768",
                {
                    "query_embedding": query_embedding,
                    "match_count": match_count,
                    "filter": {},
                }
            ).execute()
            if not docs_result.data:
                logger.info("No relevant documentation found for query")
                return "No relevant documentation found for your query."
            formatted_chunks = []
            for doc in docs_result.data:
                similarity_value = doc.get('similarity', 0)
                if isinstance(similarity_value, str):
                    similarity_pct = similarity_value
                else:
                    similarity_pct = f"{float(similarity_value) * 100:.2f}%"
                chunk_text = f"""
                # {doc.get('title', 'Document')} (Relevance: {similarity_pct})

                {doc.get('content', '')}

                Source: documents | ID: {doc.get('id', '')}
                """
                formatted_chunks.append(chunk_text.strip())
            return "\n\n---\n\n".join(formatted_chunks)
        except Exception as e:
            logger.error(f"Error retrieving documents: {e}", exc_info=True)
            return f"Error retrieving documents: {e}"

    def retrieve_feedback(self, query: str, match_count: int = 5) -> str:
        """Retrieve relevant user feedback from the feedback_768 table via Supabase RPC.

        Args:
            query (str): The user's question or query.
            match_count (int): Number of matches to return (default: 5).

        Returns:
            str: Formatted string containing the most relevant feedback entries, or an error message.
        """
        if not self.supabase_client:
            logger.error("Supabase client not available in ToolManager")
            return "Error: Supabase connection not available."
        try:
            logger.debug(f"Retrieving feedback for query: '{query[:50]}...'")
            query_embedding = self.create_embedding(query)
            feedback_result = self.supabase_client.rpc(
                "match_feedback_768",
                {
                    "query_embedding": query_embedding,
                    "match_count": match_count,
                    "filter": {},
                }
            ).execute()
            if not feedback_result.data:
                logger.info("No relevant user feedback found for query")
                return "No relevant user feedback found for your query."
            formatted_chunks = []
            for item in feedback_result.data:
                similarity_value = item.get('similarity', 0)
                if isinstance(similarity_value, str):
                    similarity_pct = similarity_value
                else:
                    similarity_pct = f"{float(similarity_value) * 100:.2f}%"
                is_helpful = item.get("is_helpful", True)
                helpful_indicator = "👍 Helpful" if is_helpful else "👎 Not Helpful"
                chunk_text = f"""
                # {item.get('title', 'User Feedback')} (Relevance: {similarity_pct})

                {item.get('content', '')}

                {helpful_indicator} | Source: feedback | ID: {item.get('id', '')}
                """
                formatted_chunks.append(chunk_text.strip())
            return "\n\n---\n\n".join(formatted_chunks)
        except Exception as e:
            logger.error(f"Error retrieving feedback: {e}", exc_info=True)
            return f"Error retrieving feedback: {e}"

    def get_rule_id_from_user_query(self, user_query: str, match_count: int = 3) -> list[str]:
        """
        Retrieve the most relevant rule ID(s) from the rule engine based on a user's complaint or query.

        This tool is designed for the AI Agent to map a customer's complaint or natural language query to the corresponding rule(s) in the rule engine. It uses vector similarity search (via the `match_rule_engine_768` Supabase RPC on the `rule_engine_768` table) to find the best-matching rule(s) for the input query. The returned rule ID(s) can then be used to fetch the full rule or rule flow from the MCP or Supabase.

        Args:
            user_query (str): The customer's complaint or query in natural language.
            match_count (int, optional): Number of top rule IDs to return. Defaults to 3 (best matches).

        Returns:
            list[str]: List of rule IDs (as strings) most relevant to the user query. Returns an empty list if no match is found or on error.

        Example:
            >>> tm = ToolManager()
            >>> rule_ids = tm.get_rule_id_from_user_query('My scooter won\'t start')
            >>> print(rule_ids)
            ['001']
        """
        import re
        logger.info(f"[get_rule_id_from_user_query] Called with user_query={user_query!r}, match_count={match_count!r}")
        if not self.supabase_client:
            logger.error("Supabase client not available in ToolManager")
            return []
        try:
            logger.debug(f"[get_rule_id_from_user_query] Creating embedding for user_query: {user_query!r}")
            query_embedding = self.create_embedding(user_query)
            logger.debug(f"[get_rule_id_from_user_query] Embedding: {query_embedding[:10]}... (len={len(query_embedding)})")
            logger.debug(f"[get_rule_id_from_user_query] Calling Supabase RPC 'match_rule_mapping_768' with embedding and match_count={match_count}")
            rules_result = self.supabase_client.rpc(
                "match_rule_mapping_768",
                {
                    "query_embedding": query_embedding,
                    "match_count": match_count,
                    "filter": {},
                }
            ).execute()
            logger.debug(f"[get_rule_id_from_user_query] Supabase RPC response: {rules_result}")
            if not rules_result.data:
                logger.info("[get_rule_id_from_user_query] No relevant rule found for user query")
                return []
            rule_ids = []
            for row in rules_result.data:
                content = row.get('content', '')
                logger.debug(f"[get_rule_id_from_user_query] Row content: {content}")
                # Find all rule IDs after TRUE : or FALSE : (e.g., TRUE : 026, FALSE : 004)
                matches = re.findall(r'\b(?:TRUE|FALSE)\s*:\s*(\d+)\b', content)
                logger.debug(f"[get_rule_id_from_user_query] Extracted matches from content: {matches}")
                rule_ids.extend(matches)
            logger.info(f"[get_rule_id_from_user_query] Extracted rule IDs: {rule_ids}")
            return rule_ids
        except Exception as e:
            logger.error(f"[get_rule_id_from_user_query] Error retrieving rule ID(s): {e}", exc_info=True)
            return []

if __name__ == "__main__":
    # Simple tests for ToolManager embedding and retrieval methods
    tm = ToolManager()

    print("\nTesting get_rule_id_from_user_query:")
    try:
        user_query = "Display is off but vehicle is drivable"
        rule_ids = tm.get_rule_id_from_user_query(user_query)
        print(f"Rule IDs for '{user_query}': {rule_ids}")
        assert rule_ids == ["24"], f"Expected ['24'], got {rule_ids}"
        print("Test passed: Correct rule ID returned.")
    except Exception as e:
        print(f"Error in get_rule_id_from_user_query: {e}")
