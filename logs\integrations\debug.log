2025-05-12 12:13:01,298 INFO Inserting 1 vectors into collection mem0migrations
2025-05-12 12:13:01,376 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 12:13:01,376 INFO Adding memory for user_id=test_user
2025-05-12 12:13:03,361 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 12:13:04,178 INFO Total existing memories: 0
2025-05-12 12:13:04,335 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 12:13:05,152 INFO Retrieving memories for user_id=test_user, query='France'
2025-05-12 12:25:35,503 INFO Creating new collection: mem0_memories
2025-05-12 12:25:36,746 INFO Successfully created collection mem0_memories with dimension 1536
2025-05-12 12:25:41,660 INFO Creating new collection: mem0migrations
2025-05-12 12:25:43,464 INFO Successfully created collection mem0migrations with dimension 1536
2025-05-12 12:25:43,800 INFO Inserting 1 vectors into collection mem0migrations
2025-05-12 12:25:44,197 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 12:25:44,198 INFO Adding memory for user_id=test_user
2025-05-12 12:25:46,051 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 12:25:47,478 INFO Total existing memories: 0
2025-05-12 12:25:47,620 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 12:25:49,695 INFO Retrieving memories for user_id=test_user, query='France'
2025-05-12 12:29:02,134 INFO Creating new collection: mem0_memories
2025-05-12 12:29:02,462 ERROR Failed to create collection: Dimensions reported by adapter, dimension, and existing collection do not match
2025-05-12 12:30:58,072 INFO Creating new collection: mem0_memories
2025-05-12 12:30:58,436 ERROR Failed to create collection: Dimensions reported by adapter, dimension, and existing collection do not match
2025-05-12 12:36:22,704 INFO Creating new collection: mem0_memories768
2025-05-12 12:36:23,723 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 12:36:27,727 INFO Creating new collection: mem0migrations
2025-05-12 12:36:28,030 ERROR Failed to create collection: Dimensions reported by adapter, dimension, and existing collection do not match
2025-05-12 12:37:18,075 INFO Creating new collection: mem0_memories768
2025-05-12 12:37:18,717 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 12:37:26,047 INFO Creating new collection: mem0migrations
2025-05-12 12:37:28,419 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 12:37:29,009 INFO Inserting 1 vectors into collection mem0migrations
2025-05-12 12:37:29,667 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 12:37:29,667 INFO Adding memory for user_id=test_user
2025-05-12 12:37:31,015 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 12:37:31,968 INFO Total existing memories: 0
2025-05-12 12:37:32,071 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 12:37:34,977 INFO Retrieving memories for user_id=test_user, query='France'
2025-05-12 12:39:40,174 INFO Creating new collection: mem0_memories768
2025-05-12 12:39:41,969 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 12:39:48,555 INFO Creating new collection: mem0migrations
2025-05-12 12:39:50,278 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 12:39:50,824 INFO Inserting 1 vectors into collection mem0migrations
2025-05-12 12:39:51,401 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 12:39:51,403 INFO Adding memory for user_id=test_user
2025-05-12 12:39:52,931 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 12:39:53,759 INFO Total existing memories: 0
2025-05-12 12:39:53,873 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 12:39:55,801 INFO Retrieving memories for user_id=test_user, query='France'
2025-05-12 12:45:44,911 INFO Creating new collection: mem0_memories768
2025-05-12 12:45:47,008 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 12:45:53,813 INFO Creating new collection: mem0migrations
2025-05-12 12:45:55,755 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 12:45:56,431 INFO Inserting 1 vectors into collection mem0migrations
2025-05-12 12:45:57,193 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 12:45:57,193 INFO Adding memory for user_id=test_user
2025-05-12 12:45:58,661 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 12:45:59,364 INFO Total existing memories: 0
2025-05-12 12:45:59,492 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 12:46:00,867 INFO Retrieving memories for user_id=test_user, query='France'
2025-05-12 12:54:31,926 INFO Creating new collection: mem0_memories768
2025-05-12 12:54:32,690 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 12:54:36,482 INFO Creating new collection: mem0migrations
2025-05-12 12:54:37,298 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 12:54:37,562 INFO Inserting 1 vectors into collection mem0migrations
2025-05-12 12:54:37,883 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 12:54:37,884 INFO Adding memory for user_id=test_user
2025-05-12 12:54:39,075 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 12:54:41,334 INFO Total existing memories: 0
2025-05-12 12:54:41,425 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 12:54:43,501 INFO {'id': '0', 'text': 'Likes Polar Bears', 'event': 'ADD'}
2025-05-12 12:54:46,188 INFO Inserting 1 vectors into collection mem0_memories768
2025-05-12 12:54:46,937 INFO Retrieving memories for user_id=test_user, query='Polar Bears'
2025-05-12 15:14:49,276 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 15:14:50,366 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 15:14:50,368 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 15:14:50,371 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:14:50,391 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:17:17,384 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 15:17:18,678 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 15:17:18,679 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 15:17:18,684 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:17:18,704 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:17:34,684 INFO Creating new collection: mem0_memories768
2025-05-12 15:17:36,799 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:17:42,026 INFO Creating new collection: mem0migrations
2025-05-12 15:17:43,953 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:17:44,507 INFO Inserting 1 vectors into collection mem0migrations
2025-05-12 15:17:45,165 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:17:45,165 INFO Retrieving memories for user_id=user-a5smgkld, query='Hi'
2025-05-12 15:17:49,532 INFO Retrieved memory for user_id=user-a5smgkld, query='Hi': {'results': []}
2025-05-12 15:17:49,538 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 15:17:50,111 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:17:50,112 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=a722f18692b74b839e288896d62ac5eb
2025-05-12 15:17:50,112 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=a722f18692b74b839e288896d62ac5eb
2025-05-12 15:17:50,127 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a722f18692b74b839e288896d62ac5eb "HTTP/1.1 202 Accepted"
2025-05-12 15:17:50,142 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 15:17:50,717 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:17:50,719 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=847b34bf19b144e98ca083cd983f2807
2025-05-12 15:17:50,719 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=847b34bf19b144e98ca083cd983f2807
2025-05-12 15:17:50,722 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a722f18692b74b839e288896d62ac5eb "HTTP/1.1 202 Accepted"
2025-05-12 15:17:50,732 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=847b34bf19b144e98ca083cd983f2807 "HTTP/1.1 202 Accepted"
2025-05-12 15:17:50,794 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a722f18692b74b839e288896d62ac5eb "HTTP/1.1 202 Accepted"
2025-05-12 15:17:50,804 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=847b34bf19b144e98ca083cd983f2807 "HTTP/1.1 202 Accepted"
2025-05-12 15:17:50,856 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=847b34bf19b144e98ca083cd983f2807 "HTTP/1.1 202 Accepted"
2025-05-12 15:17:52,309 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:17:57,494 INFO Creating new collection: mem0_memories768
2025-05-12 15:17:59,534 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:18:05,007 INFO Creating new collection: mem0migrations
2025-05-12 15:18:07,132 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:18:07,737 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:18:07,738 INFO Adding memory for user_id=user-a5smgkld
2025-05-12 15:18:09,149 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:18:10,198 INFO Total existing memories: 0
2025-05-12 15:18:10,319 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:18:11,691 INFO Stored memory for user_id=user-a5smgkld
2025-05-12 15:19:27,938 INFO Creating new collection: mem0_memories768
2025-05-12 15:19:29,102 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:19:32,636 INFO Creating new collection: mem0migrations
2025-05-12 15:19:33,618 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:19:34,057 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:19:34,058 INFO Retrieving memories for user_id=user-a5smgkld, query='I like pink elephants '
2025-05-12 15:19:42,396 INFO Retrieved memory for user_id=user-a5smgkld, query='I like pink elephants ': {'results': []}
2025-05-12 15:20:28,168 INFO Creating new collection: mem0_memories768
2025-05-12 15:20:29,539 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:20:33,094 INFO Creating new collection: mem0migrations
2025-05-12 15:20:34,181 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:20:34,495 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:20:34,496 INFO Retrieving memories for user_id=user-7jpwvajc, query='Hi'
2025-05-12 15:20:42,441 INFO Retrieved memory for user_id=user-7jpwvajc, query='Hi': {'results': []}
2025-05-12 15:20:42,451 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 15:20:43,160 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:20:43,161 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=5165101f6cb64fdfb2972d92ca3ac1e8
2025-05-12 15:20:43,161 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=5165101f6cb64fdfb2972d92ca3ac1e8
2025-05-12 15:20:43,170 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=5165101f6cb64fdfb2972d92ca3ac1e8 "HTTP/1.1 202 Accepted"
2025-05-12 15:20:43,174 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 15:20:43,797 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:20:43,798 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=4b4fe3b8a72f45639374ce7ce55bde28
2025-05-12 15:20:43,799 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=4b4fe3b8a72f45639374ce7ce55bde28
2025-05-12 15:20:43,806 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=4b4fe3b8a72f45639374ce7ce55bde28 "HTTP/1.1 202 Accepted"
2025-05-12 15:20:43,816 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=5165101f6cb64fdfb2972d92ca3ac1e8 "HTTP/1.1 202 Accepted"
2025-05-12 15:20:43,868 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=4b4fe3b8a72f45639374ce7ce55bde28 "HTTP/1.1 202 Accepted"
2025-05-12 15:20:43,869 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=5165101f6cb64fdfb2972d92ca3ac1e8 "HTTP/1.1 202 Accepted"
2025-05-12 15:20:43,920 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=4b4fe3b8a72f45639374ce7ce55bde28 "HTTP/1.1 202 Accepted"
2025-05-12 15:20:44,857 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:20:47,501 INFO Creating new collection: mem0_memories768
2025-05-12 15:20:48,697 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:20:52,508 INFO Creating new collection: mem0migrations
2025-05-12 15:20:54,085 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:20:54,421 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:20:54,422 INFO Adding memory for user_id=user-7jpwvajc
2025-05-12 15:20:55,259 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:20:56,139 INFO Total existing memories: 0
2025-05-12 15:20:56,307 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:20:57,616 INFO Stored memory for user_id=user-7jpwvajc
2025-05-12 15:24:44,685 INFO Creating new collection: mem0_memories768
2025-05-12 15:24:46,228 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:24:49,591 INFO Creating new collection: mem0migrations
2025-05-12 15:24:50,774 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:24:51,210 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:24:51,210 INFO Retrieving memories for user_id=user-c87kox7s, query='Which llm are you ?'
2025-05-12 15:24:54,067 INFO Retrieved memory for user_id=user-c87kox7s, query='Which llm are you ?': {'results': []}
2025-05-12 15:24:54,074 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 15:24:54,646 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:24:54,647 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=cbfb39806cde4d52a091003fdcf18307
2025-05-12 15:24:54,647 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=cbfb39806cde4d52a091003fdcf18307
2025-05-12 15:24:54,653 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=cbfb39806cde4d52a091003fdcf18307 "HTTP/1.1 202 Accepted"
2025-05-12 15:24:54,655 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 15:24:55,181 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:24:55,182 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=2c2cd375d873472ea85a9444e3c959ce
2025-05-12 15:24:55,183 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=2c2cd375d873472ea85a9444e3c959ce
2025-05-12 15:24:55,188 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2c2cd375d873472ea85a9444e3c959ce "HTTP/1.1 202 Accepted"
2025-05-12 15:24:55,204 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=cbfb39806cde4d52a091003fdcf18307 "HTTP/1.1 202 Accepted"
2025-05-12 15:24:55,235 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2c2cd375d873472ea85a9444e3c959ce "HTTP/1.1 202 Accepted"
2025-05-12 15:24:55,257 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=cbfb39806cde4d52a091003fdcf18307 "HTTP/1.1 202 Accepted"
2025-05-12 15:24:55,298 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2c2cd375d873472ea85a9444e3c959ce "HTTP/1.1 202 Accepted"
2025-05-12 15:24:56,376 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:24:59,836 INFO Creating new collection: mem0_memories768
2025-05-12 15:25:01,055 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:25:04,718 INFO Creating new collection: mem0migrations
2025-05-12 15:25:05,868 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:25:06,355 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:25:06,355 INFO Adding memory for user_id=user-c87kox7s
2025-05-12 15:25:07,039 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:25:07,783 INFO Total existing memories: 0
2025-05-12 15:25:07,960 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:25:08,938 INFO Stored memory for user_id=user-c87kox7s
2025-05-12 15:28:54,944 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 15:28:56,024 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 15:28:56,025 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 15:28:56,029 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:28:56,049 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:32:29,240 INFO Creating new collection: mem0_memories768
2025-05-12 15:32:29,904 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:32:32,792 INFO Creating new collection: mem0migrations
2025-05-12 15:32:33,487 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:32:33,730 INFO Inserting 1 vectors into collection mem0migrations
2025-05-12 15:32:34,006 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:32:34,007 INFO Retrieving memories for user_id=user-5xo9et9r, query='Check'
2025-05-12 15:32:42,112 INFO Retrieved memory for user_id=user-5xo9et9r, query='Check': {'results': []}
2025-05-12 15:32:42,231 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 15:32:42,908 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:32:42,909 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=9b4cafc51c774e49be0665c087d175fa
2025-05-12 15:32:42,910 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=9b4cafc51c774e49be0665c087d175fa
2025-05-12 15:32:42,918 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9b4cafc51c774e49be0665c087d175fa "HTTP/1.1 202 Accepted"
2025-05-12 15:32:42,921 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 15:32:43,644 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9b4cafc51c774e49be0665c087d175fa "HTTP/1.1 202 Accepted"
2025-05-12 15:32:43,647 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:32:43,648 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=024c19a0b48f467c9ed9abca524dc286
2025-05-12 15:32:43,648 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=024c19a0b48f467c9ed9abca524dc286
2025-05-12 15:32:43,655 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=024c19a0b48f467c9ed9abca524dc286 "HTTP/1.1 202 Accepted"
2025-05-12 15:32:43,717 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=024c19a0b48f467c9ed9abca524dc286 "HTTP/1.1 202 Accepted"
2025-05-12 15:32:43,718 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9b4cafc51c774e49be0665c087d175fa "HTTP/1.1 202 Accepted"
2025-05-12 15:32:43,770 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=024c19a0b48f467c9ed9abca524dc286 "HTTP/1.1 202 Accepted"
2025-05-12 15:32:44,483 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:32:44,540 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9b4cafc51c774e49be0665c087d175fa "HTTP/1.1 202 Accepted"
2025-05-12 15:32:44,603 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=024c19a0b48f467c9ed9abca524dc286 "HTTP/1.1 202 Accepted"
2025-05-12 15:32:44,654 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=024c19a0b48f467c9ed9abca524dc286 "HTTP/1.1 202 Accepted"
2025-05-12 15:32:44,706 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=024c19a0b48f467c9ed9abca524dc286 "HTTP/1.1 202 Accepted"
2025-05-12 15:32:44,717 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9b4cafc51c774e49be0665c087d175fa "HTTP/1.1 202 Accepted"
2025-05-12 15:32:45,142 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:32:46,892 INFO Creating new collection: mem0_memories768
2025-05-12 15:32:47,650 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:32:50,251 INFO Creating new collection: mem0migrations
2025-05-12 15:32:51,026 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:32:51,272 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:32:51,273 INFO Adding memory for user_id=user-5xo9et9r
2025-05-12 15:32:52,531 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:32:53,309 INFO Total existing memories: 0
2025-05-12 15:32:53,433 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:32:54,420 INFO Stored memory for user_id=user-5xo9et9r
2025-05-12 15:33:19,944 INFO Creating new collection: mem0_memories768
2025-05-12 15:33:20,821 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:33:23,260 INFO Creating new collection: mem0migrations
2025-05-12 15:33:24,026 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:33:24,265 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:33:24,265 INFO Retrieving memories for user_id=user-5xo9et9r, query='I like pink elephants'
2025-05-12 15:33:29,211 INFO Retrieved memory for user_id=user-5xo9et9r, query='I like pink elephants': {'results': []}
2025-05-12 15:33:29,242 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 15:33:29,891 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:33:29,892 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=b71a15b63945454ba130011cc5eeac35
2025-05-12 15:33:29,893 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=b71a15b63945454ba130011cc5eeac35
2025-05-12 15:33:29,912 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b71a15b63945454ba130011cc5eeac35 "HTTP/1.1 202 Accepted"
2025-05-12 15:33:29,915 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 15:33:30,851 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:33:30,852 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=433cc730902b4672a1c2686ebcbb4327
2025-05-12 15:33:30,853 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=433cc730902b4672a1c2686ebcbb4327
2025-05-12 15:33:30,859 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b71a15b63945454ba130011cc5eeac35 "HTTP/1.1 202 Accepted"
2025-05-12 15:33:30,866 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=433cc730902b4672a1c2686ebcbb4327 "HTTP/1.1 202 Accepted"
2025-05-12 15:33:30,929 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=433cc730902b4672a1c2686ebcbb4327 "HTTP/1.1 202 Accepted"
2025-05-12 15:33:30,931 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b71a15b63945454ba130011cc5eeac35 "HTTP/1.1 202 Accepted"
2025-05-12 15:33:30,980 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=433cc730902b4672a1c2686ebcbb4327 "HTTP/1.1 202 Accepted"
2025-05-12 15:33:31,704 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:33:39,977 INFO HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:batchEmbedContents "HTTP/1.1 200 OK"
2025-05-12 15:33:41,516 INFO HTTP Request: POST https://gttwzegcmextyljehehj.supabase.co/rest/v1/rpc/match_feedback_768 "HTTP/2 200 OK"
2025-05-12 15:33:41,526 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=433cc730902b4672a1c2686ebcbb4327 "HTTP/1.1 202 Accepted"
2025-05-12 15:33:41,527 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b71a15b63945454ba130011cc5eeac35 "HTTP/1.1 202 Accepted"
2025-05-12 15:33:42,151 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:33:45,637 INFO Creating new collection: mem0_memories768
2025-05-12 15:33:46,646 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:33:49,676 INFO Creating new collection: mem0migrations
2025-05-12 15:33:50,719 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:33:51,098 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:33:51,098 INFO Adding memory for user_id=user-5xo9et9r
2025-05-12 15:33:51,368 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:33:57,420 INFO Total existing memories: 0
2025-05-12 15:33:57,524 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:33:58,779 INFO {'id': '0', 'text': 'Likes pink elephants', 'event': 'ADD'}
2025-05-12 15:33:59,327 INFO Inserting 1 vectors into collection mem0_memories768
2025-05-12 15:34:00,433 INFO Stored memory for user_id=user-5xo9et9r
2025-05-12 15:34:25,014 INFO Creating new collection: mem0_memories768
2025-05-12 15:34:26,188 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:34:29,487 INFO Creating new collection: mem0migrations
2025-05-12 15:34:30,597 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:34:30,992 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:34:30,993 INFO Retrieving memories for user_id=user-5xo9et9r, query='Which animals do I like ?'
2025-05-12 15:34:39,239 INFO Retrieved memory for user_id=user-5xo9et9r, query='Which animals do I like ?': {'results': [{'id': 'cd81a25b-a0da-4aea-bc64-db3b5ade291f', 'memory': 'Likes pink elephants', 'hash': 'f27fef4ff551cbb425b743de39dd2d41', 'metadata': None, 'score': 0.43267021497512, 'created_at': '2025-05-12T03:03:59.327597-07:00', 'updated_at': None, 'user_id': 'user-5xo9et9r'}]}
2025-05-12 15:34:39,281 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 15:34:39,960 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:34:39,961 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=c48fc32789494e6ab72c1dc2b09daf41
2025-05-12 15:34:39,962 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=c48fc32789494e6ab72c1dc2b09daf41
2025-05-12 15:34:39,971 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=c48fc32789494e6ab72c1dc2b09daf41 "HTTP/1.1 202 Accepted"
2025-05-12 15:34:39,975 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 15:34:40,759 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:34:40,760 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=7d3e95da23c74abf944170c39fcb9966
2025-05-12 15:34:40,760 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=7d3e95da23c74abf944170c39fcb9966
2025-05-12 15:34:40,766 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=7d3e95da23c74abf944170c39fcb9966 "HTTP/1.1 202 Accepted"
2025-05-12 15:34:40,770 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=c48fc32789494e6ab72c1dc2b09daf41 "HTTP/1.1 202 Accepted"
2025-05-12 15:34:40,822 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=7d3e95da23c74abf944170c39fcb9966 "HTTP/1.1 202 Accepted"
2025-05-12 15:34:40,832 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=c48fc32789494e6ab72c1dc2b09daf41 "HTTP/1.1 202 Accepted"
2025-05-12 15:34:40,874 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=7d3e95da23c74abf944170c39fcb9966 "HTTP/1.1 202 Accepted"
2025-05-12 15:34:41,734 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:34:44,607 INFO HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:batchEmbedContents "HTTP/1.1 200 OK"
2025-05-12 15:34:45,553 INFO HTTP Request: POST https://gttwzegcmextyljehehj.supabase.co/rest/v1/rpc/match_feedback_768 "HTTP/2 200 OK"
2025-05-12 15:34:45,607 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=7d3e95da23c74abf944170c39fcb9966 "HTTP/1.1 202 Accepted"
2025-05-12 15:34:45,608 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=c48fc32789494e6ab72c1dc2b09daf41 "HTTP/1.1 202 Accepted"
2025-05-12 15:34:46,101 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:34:48,345 INFO Creating new collection: mem0_memories768
2025-05-12 15:34:49,235 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:34:51,490 INFO Creating new collection: mem0migrations
2025-05-12 15:34:52,216 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:34:52,521 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:34:52,522 INFO Adding memory for user_id=user-5xo9et9r
2025-05-12 15:34:52,971 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:34:55,242 INFO Total existing memories: 1
2025-05-12 15:34:55,354 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:34:56,727 INFO {'id': '0', 'text': 'Likes pink elephants', 'event': 'NONE'}
2025-05-12 15:34:56,728 INFO NOOP for Memory.
2025-05-12 15:34:57,016 INFO Stored memory for user_id=user-5xo9et9r
2025-05-12 15:35:10,290 INFO Creating new collection: mem0_memories768
2025-05-12 15:35:11,164 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:35:13,435 INFO Creating new collection: mem0migrations
2025-05-12 15:35:14,204 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:35:14,482 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:35:14,482 INFO Retrieving memories for user_id=user-eriu2jdi, query='Which animals do I like ?'
2025-05-12 15:35:16,027 INFO Retrieved memory for user_id=user-eriu2jdi, query='Which animals do I like ?': {'results': []}
2025-05-12 15:35:16,057 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 15:35:16,604 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:35:16,605 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=a38846c0eff546ad909bdf8c7cd4a0bb
2025-05-12 15:35:16,605 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=a38846c0eff546ad909bdf8c7cd4a0bb
2025-05-12 15:35:16,612 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a38846c0eff546ad909bdf8c7cd4a0bb "HTTP/1.1 202 Accepted"
2025-05-12 15:35:16,614 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 15:35:17,157 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:35:17,158 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=2aeb97ec7127432b9037c9aaa8dd2368
2025-05-12 15:35:17,158 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=2aeb97ec7127432b9037c9aaa8dd2368
2025-05-12 15:35:17,175 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a38846c0eff546ad909bdf8c7cd4a0bb "HTTP/1.1 202 Accepted"
2025-05-12 15:35:17,176 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2aeb97ec7127432b9037c9aaa8dd2368 "HTTP/1.1 202 Accepted"
2025-05-12 15:35:17,227 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2aeb97ec7127432b9037c9aaa8dd2368 "HTTP/1.1 202 Accepted"
2025-05-12 15:35:17,227 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a38846c0eff546ad909bdf8c7cd4a0bb "HTTP/1.1 202 Accepted"
2025-05-12 15:35:17,279 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2aeb97ec7127432b9037c9aaa8dd2368 "HTTP/1.1 202 Accepted"
2025-05-12 15:35:17,971 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:40:56,722 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 15:40:57,793 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 15:40:57,794 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 15:40:57,803 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:40:57,832 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:41:33,451 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 15:41:34,783 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 15:41:34,784 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 15:41:34,788 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:41:34,811 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:41:54,489 INFO Creating new collection: mem0_memories768
2025-05-12 15:41:55,101 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:41:57,080 INFO Creating new collection: mem0migrations
2025-05-12 15:41:57,638 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:41:57,800 INFO Inserting 1 vectors into collection mem0migrations
2025-05-12 15:41:58,022 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:41:58,022 INFO Retrieving memories for user_id=user-kax3rnt4, query='I like rule 100 very much'
2025-05-12 15:41:59,525 INFO Retrieved memory for user_id=user-kax3rnt4, query='I like rule 100 very much': {'results': []}
2025-05-12 15:41:59,566 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 15:42:00,131 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:42:00,132 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=941f13a80edc4b568dcd239833a81709
2025-05-12 15:42:00,132 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=941f13a80edc4b568dcd239833a81709
2025-05-12 15:42:00,139 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=941f13a80edc4b568dcd239833a81709 "HTTP/1.1 202 Accepted"
2025-05-12 15:42:00,142 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 15:42:01,058 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:42:01,059 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=cde85070438a4ad685f5892b93aedc0e
2025-05-12 15:42:01,060 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=cde85070438a4ad685f5892b93aedc0e
2025-05-12 15:42:01,065 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=941f13a80edc4b568dcd239833a81709 "HTTP/1.1 202 Accepted"
2025-05-12 15:42:01,069 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=cde85070438a4ad685f5892b93aedc0e "HTTP/1.1 202 Accepted"
2025-05-12 15:42:01,128 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=cde85070438a4ad685f5892b93aedc0e "HTTP/1.1 202 Accepted"
2025-05-12 15:42:01,129 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=941f13a80edc4b568dcd239833a81709 "HTTP/1.1 202 Accepted"
2025-05-12 15:42:01,180 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=cde85070438a4ad685f5892b93aedc0e "HTTP/1.1 202 Accepted"
2025-05-12 15:42:02,328 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:42:02,383 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=941f13a80edc4b568dcd239833a81709 "HTTP/1.1 202 Accepted"
2025-05-12 15:42:02,435 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=cde85070438a4ad685f5892b93aedc0e "HTTP/1.1 202 Accepted"
2025-05-12 15:42:02,488 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=cde85070438a4ad685f5892b93aedc0e "HTTP/1.1 202 Accepted"
2025-05-12 15:42:03,084 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=cde85070438a4ad685f5892b93aedc0e "HTTP/1.1 202 Accepted"
2025-05-12 15:42:03,086 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=941f13a80edc4b568dcd239833a81709 "HTTP/1.1 202 Accepted"
2025-05-12 15:42:03,980 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:42:05,580 INFO Creating new collection: mem0_memories768
2025-05-12 15:42:06,190 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:42:07,945 INFO Creating new collection: mem0migrations
2025-05-12 15:42:08,561 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:42:08,800 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:42:08,801 INFO Adding memory for user_id=user-kax3rnt4
2025-05-12 15:42:10,177 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:42:13,082 INFO Total existing memories: 0
2025-05-12 15:42:13,172 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:42:14,378 INFO {'id': '0', 'text': 'Likes rule 100 very much', 'event': 'ADD'}
2025-05-12 15:42:14,853 INFO Inserting 1 vectors into collection mem0_memories768
2025-05-12 15:42:15,566 INFO Stored memory for user_id=user-kax3rnt4
2025-05-12 15:44:56,528 INFO Creating new collection: mem0_memories768
2025-05-12 15:44:58,332 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:45:02,680 INFO Creating new collection: mem0migrations
2025-05-12 15:45:04,347 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:45:04,899 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:45:04,899 INFO Retrieving memories for user_id=user-kax3rnt4, query='Which rule do I like ?'
2025-05-12 15:45:08,007 INFO Retrieved memory for user_id=user-kax3rnt4, query='Which rule do I like ?': {'results': [{'id': 'a865909f-7a34-4625-baba-c9da8781d8df', 'memory': 'Likes rule 100 very much', 'hash': '9e22a5769bb46570bfa1e780af12bb89', 'metadata': None, 'score': 0.339316515561216, 'created_at': '2025-05-12T03:12:14.853754-07:00', 'updated_at': None, 'user_id': 'user-kax3rnt4'}]}
2025-05-12 15:45:08,050 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 15:45:08,611 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:45:08,612 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=49c7a4078c7841e3b73ad5b9f7e8e862
2025-05-12 15:45:08,613 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=49c7a4078c7841e3b73ad5b9f7e8e862
2025-05-12 15:45:08,618 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=49c7a4078c7841e3b73ad5b9f7e8e862 "HTTP/1.1 202 Accepted"
2025-05-12 15:45:08,619 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 15:45:09,158 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:45:09,159 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=e8390092c6c2495aaa8079e74c899b21
2025-05-12 15:45:09,159 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=e8390092c6c2495aaa8079e74c899b21
2025-05-12 15:45:09,164 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e8390092c6c2495aaa8079e74c899b21 "HTTP/1.1 202 Accepted"
2025-05-12 15:45:09,188 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=49c7a4078c7841e3b73ad5b9f7e8e862 "HTTP/1.1 202 Accepted"
2025-05-12 15:45:09,219 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e8390092c6c2495aaa8079e74c899b21 "HTTP/1.1 202 Accepted"
2025-05-12 15:45:09,251 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=49c7a4078c7841e3b73ad5b9f7e8e862 "HTTP/1.1 202 Accepted"
2025-05-12 15:45:09,282 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e8390092c6c2495aaa8079e74c899b21 "HTTP/1.1 202 Accepted"
2025-05-12 15:45:10,361 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:45:10,417 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=49c7a4078c7841e3b73ad5b9f7e8e862 "HTTP/1.1 202 Accepted"
2025-05-12 15:45:10,470 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e8390092c6c2495aaa8079e74c899b21 "HTTP/1.1 202 Accepted"
2025-05-12 15:45:10,522 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e8390092c6c2495aaa8079e74c899b21 "HTTP/1.1 202 Accepted"
2025-05-12 15:45:11,658 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e8390092c6c2495aaa8079e74c899b21 "HTTP/1.1 202 Accepted"
2025-05-12 15:45:11,659 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=49c7a4078c7841e3b73ad5b9f7e8e862 "HTTP/1.1 202 Accepted"
2025-05-12 15:45:12,541 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:45:12,606 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=49c7a4078c7841e3b73ad5b9f7e8e862 "HTTP/1.1 202 Accepted"
2025-05-12 15:45:12,658 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e8390092c6c2495aaa8079e74c899b21 "HTTP/1.1 202 Accepted"
2025-05-12 15:45:12,710 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e8390092c6c2495aaa8079e74c899b21 "HTTP/1.1 202 Accepted"
2025-05-12 15:45:13,013 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e8390092c6c2495aaa8079e74c899b21 "HTTP/1.1 202 Accepted"
2025-05-12 15:45:13,023 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=49c7a4078c7841e3b73ad5b9f7e8e862 "HTTP/1.1 202 Accepted"
2025-05-12 15:45:13,939 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:45:17,919 INFO Creating new collection: mem0_memories768
2025-05-12 15:45:19,672 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:45:24,188 INFO Creating new collection: mem0migrations
2025-05-12 15:45:25,918 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:45:26,489 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:45:26,490 INFO Adding memory for user_id=user-kax3rnt4
2025-05-12 15:45:27,074 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:45:37,564 INFO Total existing memories: 1
2025-05-12 15:45:37,837 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:45:39,587 INFO {'id': '0', 'text': 'Likes rule 100', 'event': 'UPDATE', 'old_memory': 'Likes rule 100 very much'}
2025-05-12 15:45:39,588 INFO Updating memory with data='Likes rule 100'
2025-05-12 15:45:40,840 INFO Updating memory with ID memory_id='a865909f-7a34-4625-baba-c9da8781d8df' with data='Likes rule 100'
2025-05-12 15:45:41,973 INFO Stored memory for user_id=user-kax3rnt4
2025-05-12 15:47:35,324 INFO Creating new collection: mem0_memories768
2025-05-12 15:47:36,332 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:47:39,172 INFO Creating new collection: mem0migrations
2025-05-12 15:47:40,059 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:47:40,349 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:47:40,350 INFO Retrieving memories for user_id=user-ilxqdku2, query='Which Rule do I like ?'
2025-05-12 15:47:44,721 INFO Retrieved memory for user_id=user-ilxqdku2, query='Which Rule do I like ?': {'results': []}
2025-05-12 15:47:44,752 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 15:47:45,288 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:47:45,289 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=d273580bd5ed483e83ab25440d27da6a
2025-05-12 15:47:45,290 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=d273580bd5ed483e83ab25440d27da6a
2025-05-12 15:47:45,298 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d273580bd5ed483e83ab25440d27da6a "HTTP/1.1 202 Accepted"
2025-05-12 15:47:45,300 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 15:47:45,841 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:47:45,842 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=d947b2472ad94fceb811ac7de3b37055
2025-05-12 15:47:45,843 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=d947b2472ad94fceb811ac7de3b37055
2025-05-12 15:47:45,849 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d947b2472ad94fceb811ac7de3b37055 "HTTP/1.1 202 Accepted"
2025-05-12 15:47:45,852 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d273580bd5ed483e83ab25440d27da6a "HTTP/1.1 202 Accepted"
2025-05-12 15:47:45,913 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d947b2472ad94fceb811ac7de3b37055 "HTTP/1.1 202 Accepted"
2025-05-12 15:47:45,914 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d273580bd5ed483e83ab25440d27da6a "HTTP/1.1 202 Accepted"
2025-05-12 15:47:45,976 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d947b2472ad94fceb811ac7de3b37055 "HTTP/1.1 202 Accepted"
2025-05-12 15:47:57,035 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:49:04,480 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 15:49:05,668 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 15:49:05,669 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 15:49:05,673 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:49:05,696 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:49:46,869 INFO Creating new collection: mem0_memories768
2025-05-12 15:49:48,672 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:49:53,299 INFO Creating new collection: mem0migrations
2025-05-12 15:49:55,020 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:49:55,569 INFO Inserting 1 vectors into collection mem0migrations
2025-05-12 15:49:56,196 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:49:56,197 INFO Retrieving memories for user_id=user-d07mil98, query='Which tools do you have ?'
2025-05-12 15:49:58,956 INFO Retrieved memory for user_id=user-d07mil98, query='Which tools do you have ?': {'results': []}
2025-05-12 15:49:59,001 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 15:49:59,581 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:49:59,582 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=4cd6f5316a6440e9a5779be628502166
2025-05-12 15:49:59,583 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=4cd6f5316a6440e9a5779be628502166
2025-05-12 15:49:59,588 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=4cd6f5316a6440e9a5779be628502166 "HTTP/1.1 202 Accepted"
2025-05-12 15:49:59,590 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 15:50:00,121 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:50:00,122 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=80d66d60e21a409f9fc1b24296d19188
2025-05-12 15:50:00,122 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=80d66d60e21a409f9fc1b24296d19188
2025-05-12 15:50:00,127 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=80d66d60e21a409f9fc1b24296d19188 "HTTP/1.1 202 Accepted"
2025-05-12 15:50:00,143 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=4cd6f5316a6440e9a5779be628502166 "HTTP/1.1 202 Accepted"
2025-05-12 15:50:00,185 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=80d66d60e21a409f9fc1b24296d19188 "HTTP/1.1 202 Accepted"
2025-05-12 15:50:00,195 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=4cd6f5316a6440e9a5779be628502166 "HTTP/1.1 202 Accepted"
2025-05-12 15:50:00,238 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=80d66d60e21a409f9fc1b24296d19188 "HTTP/1.1 202 Accepted"
2025-05-12 15:50:00,847 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:50:07,815 INFO Creating new collection: mem0_memories768
2025-05-12 15:50:09,625 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:50:14,487 INFO Creating new collection: mem0migrations
2025-05-12 15:50:16,457 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:50:17,122 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:50:17,122 INFO Adding memory for user_id=user-d07mil98
2025-05-12 15:50:18,658 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:50:20,958 INFO Total existing memories: 0
2025-05-12 15:50:21,073 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:50:22,451 INFO Stored memory for user_id=user-d07mil98
2025-05-12 15:51:01,628 INFO Creating new collection: mem0_memories768
2025-05-12 15:51:02,603 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:51:05,657 INFO Creating new collection: mem0migrations
2025-05-12 15:51:06,931 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:51:07,402 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:51:07,403 INFO Retrieving memories for user_id=user-d07mil98, query='What does rule 100 say ?'
2025-05-12 15:51:10,461 INFO Retrieved memory for user_id=user-d07mil98, query='What does rule 100 say ?': {'results': []}
2025-05-12 15:51:10,492 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 15:51:11,115 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:51:11,115 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=52e7da15a681445e81e184b2b243811d
2025-05-12 15:51:11,116 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=52e7da15a681445e81e184b2b243811d
2025-05-12 15:51:11,123 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=52e7da15a681445e81e184b2b243811d "HTTP/1.1 202 Accepted"
2025-05-12 15:51:11,125 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 15:51:11,711 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:51:11,712 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=543417320df24ad3835f1f9dd816d5e3
2025-05-12 15:51:11,713 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=543417320df24ad3835f1f9dd816d5e3
2025-05-12 15:51:11,719 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=543417320df24ad3835f1f9dd816d5e3 "HTTP/1.1 202 Accepted"
2025-05-12 15:51:11,725 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=52e7da15a681445e81e184b2b243811d "HTTP/1.1 202 Accepted"
2025-05-12 15:51:11,777 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=543417320df24ad3835f1f9dd816d5e3 "HTTP/1.1 202 Accepted"
2025-05-12 15:51:11,779 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=52e7da15a681445e81e184b2b243811d "HTTP/1.1 202 Accepted"
2025-05-12 15:51:11,829 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=543417320df24ad3835f1f9dd816d5e3 "HTTP/1.1 202 Accepted"
2025-05-12 15:51:12,420 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:51:14,891 INFO Creating new collection: mem0_memories768
2025-05-12 15:51:16,006 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:51:19,367 INFO Creating new collection: mem0migrations
2025-05-12 15:51:20,295 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:51:20,621 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:51:20,621 INFO Adding memory for user_id=user-d07mil98
2025-05-12 15:51:20,870 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:51:21,838 INFO Total existing memories: 0
2025-05-12 15:51:21,941 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:51:23,169 INFO Stored memory for user_id=user-d07mil98
2025-05-12 15:52:58,467 INFO Creating new collection: mem0_memories768
2025-05-12 15:52:59,056 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:53:00,948 INFO Creating new collection: mem0migrations
2025-05-12 15:53:01,514 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:53:01,731 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:53:01,732 INFO Retrieving memories for user_id=user-d07mil98, query='I like rule 100 '
2025-05-12 15:53:04,234 INFO Retrieved memory for user_id=user-d07mil98, query='I like rule 100 ': {'results': []}
2025-05-12 15:53:04,265 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 15:53:04,828 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:53:04,829 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=cb5c11642c1c4a1db14c6d3a60efe589
2025-05-12 15:53:04,830 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=cb5c11642c1c4a1db14c6d3a60efe589
2025-05-12 15:53:04,836 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=cb5c11642c1c4a1db14c6d3a60efe589 "HTTP/1.1 202 Accepted"
2025-05-12 15:53:04,837 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 15:53:05,423 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:53:05,424 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=aedb5d815bb14b3fb59715f2bc46905b
2025-05-12 15:53:05,424 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=aedb5d815bb14b3fb59715f2bc46905b
2025-05-12 15:53:05,429 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=aedb5d815bb14b3fb59715f2bc46905b "HTTP/1.1 202 Accepted"
2025-05-12 15:53:05,448 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=cb5c11642c1c4a1db14c6d3a60efe589 "HTTP/1.1 202 Accepted"
2025-05-12 15:53:05,489 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=aedb5d815bb14b3fb59715f2bc46905b "HTTP/1.1 202 Accepted"
2025-05-12 15:53:05,511 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=cb5c11642c1c4a1db14c6d3a60efe589 "HTTP/1.1 202 Accepted"
2025-05-12 15:53:05,543 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=aedb5d815bb14b3fb59715f2bc46905b "HTTP/1.1 202 Accepted"
2025-05-12 15:53:06,128 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:53:07,772 INFO Creating new collection: mem0_memories768
2025-05-12 15:53:08,410 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:53:10,405 INFO Creating new collection: mem0migrations
2025-05-12 15:53:11,046 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:53:11,286 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:53:11,287 INFO Adding memory for user_id=user-d07mil98
2025-05-12 15:53:11,704 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:53:13,611 INFO Total existing memories: 0
2025-05-12 15:53:13,706 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:53:15,596 INFO {'id': '0', 'text': 'Likes rule 100', 'event': 'ADD'}
2025-05-12 15:53:16,035 INFO Inserting 1 vectors into collection mem0_memories768
2025-05-12 15:53:16,617 INFO Stored memory for user_id=user-d07mil98
2025-05-12 15:54:10,902 INFO Creating new collection: mem0_memories768
2025-05-12 15:54:11,447 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:54:13,258 INFO Creating new collection: mem0migrations
2025-05-12 15:54:13,804 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:54:14,008 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:54:14,009 INFO Retrieving memories for user_id=user-d07mil98, query='Which rule do I like ?'
2025-05-12 15:54:16,391 INFO Retrieved memory for user_id=user-d07mil98, query='Which rule do I like ?': {'results': [{'id': 'd125cf8d-b601-412e-9102-aefd06b4f649', 'memory': 'Likes rule 100', 'hash': '71c504aa39f51e6552216aa7f0f129ba', 'metadata': None, 'score': 0.405275578264134, 'created_at': '2025-05-12T03:23:16.035013-07:00', 'updated_at': None, 'user_id': 'user-d07mil98'}]}
2025-05-12 15:54:16,419 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 15:54:16,967 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:54:16,968 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=22ab534f1a5c45be858912fdfe81bae2
2025-05-12 15:54:16,969 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=22ab534f1a5c45be858912fdfe81bae2
2025-05-12 15:54:16,973 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=22ab534f1a5c45be858912fdfe81bae2 "HTTP/1.1 202 Accepted"
2025-05-12 15:54:16,975 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 15:54:17,512 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:54:17,513 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=fb475c47a53d41f48cba4b5a013a2d92
2025-05-12 15:54:17,513 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=fb475c47a53d41f48cba4b5a013a2d92
2025-05-12 15:54:17,519 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=fb475c47a53d41f48cba4b5a013a2d92 "HTTP/1.1 202 Accepted"
2025-05-12 15:54:17,531 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=22ab534f1a5c45be858912fdfe81bae2 "HTTP/1.1 202 Accepted"
2025-05-12 15:54:17,572 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=fb475c47a53d41f48cba4b5a013a2d92 "HTTP/1.1 202 Accepted"
2025-05-12 15:54:17,582 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=22ab534f1a5c45be858912fdfe81bae2 "HTTP/1.1 202 Accepted"
2025-05-12 15:54:17,625 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=fb475c47a53d41f48cba4b5a013a2d92 "HTTP/1.1 202 Accepted"
2025-05-12 15:54:18,111 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:54:19,532 INFO Creating new collection: mem0_memories768
2025-05-12 15:54:20,104 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:54:21,921 INFO Creating new collection: mem0migrations
2025-05-12 15:54:22,486 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:54:22,713 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:54:22,714 INFO Adding memory for user_id=user-d07mil98
2025-05-12 15:54:22,906 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:54:27,081 INFO Total existing memories: 1
2025-05-12 15:54:27,195 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:54:29,102 INFO {'id': '0', 'text': 'Likes rule 100', 'event': 'NONE'}
2025-05-12 15:54:29,103 INFO NOOP for Memory.
2025-05-12 15:54:29,300 INFO Stored memory for user_id=user-d07mil98
2025-05-12 15:55:03,128 INFO Creating new collection: mem0_memories768
2025-05-12 15:55:04,320 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:55:06,759 INFO Creating new collection: mem0migrations
2025-05-12 15:55:07,579 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:55:07,842 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:55:07,843 INFO Retrieving memories for user_id=user-v4vtw5zu, query='Which rule do I like ??'
2025-05-12 15:55:10,608 INFO Retrieved memory for user_id=user-v4vtw5zu, query='Which rule do I like ??': {'results': []}
2025-05-12 15:55:10,644 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 15:55:11,203 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:55:11,204 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=0ea0b2ac22c04c8eb6374be70bdafc0d
2025-05-12 15:55:11,205 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=0ea0b2ac22c04c8eb6374be70bdafc0d
2025-05-12 15:55:11,211 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=0ea0b2ac22c04c8eb6374be70bdafc0d "HTTP/1.1 202 Accepted"
2025-05-12 15:55:11,212 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 15:55:11,761 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:55:11,762 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=5cd4e745d6f1427980d328de89d21cbc
2025-05-12 15:55:11,763 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=5cd4e745d6f1427980d328de89d21cbc
2025-05-12 15:55:11,769 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5cd4e745d6f1427980d328de89d21cbc "HTTP/1.1 202 Accepted"
2025-05-12 15:55:11,783 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=0ea0b2ac22c04c8eb6374be70bdafc0d "HTTP/1.1 202 Accepted"
2025-05-12 15:55:11,825 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5cd4e745d6f1427980d328de89d21cbc "HTTP/1.1 202 Accepted"
2025-05-12 15:55:11,836 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=0ea0b2ac22c04c8eb6374be70bdafc0d "HTTP/1.1 202 Accepted"
2025-05-12 15:55:11,877 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5cd4e745d6f1427980d328de89d21cbc "HTTP/1.1 202 Accepted"
2025-05-12 15:55:12,424 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:55:16,708 INFO Creating new collection: mem0_memories768
2025-05-12 15:55:18,541 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:55:22,938 INFO Creating new collection: mem0migrations
2025-05-12 15:55:24,573 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:55:25,108 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:55:25,109 INFO Adding memory for user_id=user-v4vtw5zu
2025-05-12 15:55:25,579 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:55:26,499 INFO Total existing memories: 0
2025-05-12 15:55:26,631 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:55:27,858 INFO Stored memory for user_id=user-v4vtw5zu
2025-05-12 15:57:37,484 INFO Creating new collection: mem0_memories768
2025-05-12 15:57:39,227 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:57:43,615 INFO Creating new collection: mem0migrations
2025-05-12 15:57:45,287 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:57:45,843 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:57:45,844 INFO Retrieving memories for user_id=user-xilke2th, query='Hi'
2025-05-12 15:57:50,509 INFO Retrieved memory for user_id=user-xilke2th, query='Hi': {'results': []}
2025-05-12 15:57:50,540 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 15:57:51,081 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:57:51,082 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=10e2df3da9254545a4fe6179e9bffd98
2025-05-12 15:57:51,082 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=10e2df3da9254545a4fe6179e9bffd98
2025-05-12 15:57:51,087 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=10e2df3da9254545a4fe6179e9bffd98 "HTTP/1.1 202 Accepted"
2025-05-12 15:57:51,089 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 15:57:51,631 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:57:51,632 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=5582f32c2f324761a93523d71eee6a6b
2025-05-12 15:57:51,633 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=5582f32c2f324761a93523d71eee6a6b
2025-05-12 15:57:51,638 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5582f32c2f324761a93523d71eee6a6b "HTTP/1.1 202 Accepted"
2025-05-12 15:57:51,654 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=10e2df3da9254545a4fe6179e9bffd98 "HTTP/1.1 202 Accepted"
2025-05-12 15:57:51,686 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5582f32c2f324761a93523d71eee6a6b "HTTP/1.1 202 Accepted"
2025-05-12 15:57:51,706 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=10e2df3da9254545a4fe6179e9bffd98 "HTTP/1.1 202 Accepted"
2025-05-12 15:57:51,738 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5582f32c2f324761a93523d71eee6a6b "HTTP/1.1 202 Accepted"
2025-05-12 15:57:52,267 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:57:56,242 INFO Creating new collection: mem0_memories768
2025-05-12 15:57:57,901 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:58:02,356 INFO Creating new collection: mem0migrations
2025-05-12 15:58:04,062 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:58:04,617 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:58:04,618 INFO Adding memory for user_id=user-xilke2th
2025-05-12 15:58:05,081 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:58:06,145 INFO Total existing memories: 0
2025-05-12 15:58:06,238 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:58:07,493 INFO Stored memory for user_id=user-xilke2th
2025-05-12 15:58:16,163 INFO Creating new collection: mem0_memories768
2025-05-12 15:58:17,937 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:58:23,139 INFO Creating new collection: mem0migrations
2025-05-12 15:58:25,050 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:58:25,688 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:58:25,688 INFO Retrieving memories for user_id=user-xilke2th, query='Which llm are ?'
2025-05-12 15:58:32,142 INFO Retrieved memory for user_id=user-xilke2th, query='Which llm are ?': {'results': []}
2025-05-12 15:58:32,178 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 15:58:32,930 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 15:58:32,931 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=32e887f31bd2412e92574975feabd2df
2025-05-12 15:58:32,931 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=32e887f31bd2412e92574975feabd2df
2025-05-12 15:58:33,070 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=32e887f31bd2412e92574975feabd2df "HTTP/1.1 202 Accepted"
2025-05-12 15:58:33,071 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 15:58:33,629 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 15:58:33,630 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=6e15e7ea788240ab8ca452e675353a14
2025-05-12 15:58:33,630 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=6e15e7ea788240ab8ca452e675353a14
2025-05-12 15:58:33,636 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6e15e7ea788240ab8ca452e675353a14 "HTTP/1.1 202 Accepted"
2025-05-12 15:58:33,656 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=32e887f31bd2412e92574975feabd2df "HTTP/1.1 202 Accepted"
2025-05-12 15:58:33,697 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6e15e7ea788240ab8ca452e675353a14 "HTTP/1.1 202 Accepted"
2025-05-12 15:58:33,709 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=32e887f31bd2412e92574975feabd2df "HTTP/1.1 202 Accepted"
2025-05-12 15:58:33,760 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6e15e7ea788240ab8ca452e675353a14 "HTTP/1.1 202 Accepted"
2025-05-12 15:58:34,267 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:58:34,323 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=32e887f31bd2412e92574975feabd2df "HTTP/1.1 202 Accepted"
2025-05-12 15:58:34,375 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6e15e7ea788240ab8ca452e675353a14 "HTTP/1.1 202 Accepted"
2025-05-12 15:58:34,437 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6e15e7ea788240ab8ca452e675353a14 "HTTP/1.1 202 Accepted"
2025-05-12 15:58:34,489 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6e15e7ea788240ab8ca452e675353a14 "HTTP/1.1 202 Accepted"
2025-05-12 15:58:34,490 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=32e887f31bd2412e92574975feabd2df "HTTP/1.1 202 Accepted"
2025-05-12 15:58:34,909 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:58:39,792 INFO Creating new collection: mem0_memories768
2025-05-12 15:58:41,624 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 15:58:46,351 INFO Creating new collection: mem0migrations
2025-05-12 15:58:48,174 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 15:58:48,755 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 15:58:48,756 INFO Adding memory for user_id=user-xilke2th
2025-05-12 15:58:48,918 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:58:50,358 INFO Total existing memories: 0
2025-05-12 15:58:50,480 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 15:58:51,699 INFO Stored memory for user_id=user-xilke2th
2025-05-12 16:00:03,095 INFO Creating new collection: mem0_memories768
2025-05-12 16:00:03,640 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 16:00:05,773 INFO Creating new collection: mem0migrations
2025-05-12 16:00:06,304 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 16:00:06,509 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 16:00:06,510 INFO Retrieving memories for user_id=user-etslfgdy, query='Check'
2025-05-12 16:00:08,808 INFO Retrieved memory for user_id=user-etslfgdy, query='Check': {'results': []}
2025-05-12 16:00:08,842 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:00:09,469 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:00:09,470 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=53d4af19aeca4c31b1e9f7f1fce6300f
2025-05-12 16:00:09,470 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=53d4af19aeca4c31b1e9f7f1fce6300f
2025-05-12 16:00:09,476 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=53d4af19aeca4c31b1e9f7f1fce6300f "HTTP/1.1 202 Accepted"
2025-05-12 16:00:09,478 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:00:10,063 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:00:10,064 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=fdeb97336def45d0b628246647cb581a
2025-05-12 16:00:10,065 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=fdeb97336def45d0b628246647cb581a
2025-05-12 16:00:10,070 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=fdeb97336def45d0b628246647cb581a "HTTP/1.1 202 Accepted"
2025-05-12 16:00:10,084 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=53d4af19aeca4c31b1e9f7f1fce6300f "HTTP/1.1 202 Accepted"
2025-05-12 16:00:10,126 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=fdeb97336def45d0b628246647cb581a "HTTP/1.1 202 Accepted"
2025-05-12 16:00:10,137 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=53d4af19aeca4c31b1e9f7f1fce6300f "HTTP/1.1 202 Accepted"
2025-05-12 16:00:10,179 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=fdeb97336def45d0b628246647cb581a "HTTP/1.1 202 Accepted"
2025-05-12 16:00:10,830 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:00:56,654 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 16:00:57,964 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 16:00:57,965 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 16:00:57,970 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:00:57,991 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:01:12,662 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 16:01:13,767 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 16:01:13,769 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 16:01:13,772 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:01:13,793 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:01:43,916 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 16:01:44,998 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 16:01:44,999 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 16:01:45,003 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:01:45,025 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:02:04,690 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 16:02:05,890 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 16:02:05,891 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 16:02:05,895 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:02:05,918 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:02:23,247 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:02:23,803 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:02:23,804 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=afcb4008fae84c9daaf70ff41b4fed38
2025-05-12 16:02:23,804 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=afcb4008fae84c9daaf70ff41b4fed38
2025-05-12 16:02:23,810 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=afcb4008fae84c9daaf70ff41b4fed38 "HTTP/1.1 202 Accepted"
2025-05-12 16:02:23,811 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:02:24,337 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:02:24,338 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=bcd0304632bc409cbbdf5ceb7ee33714
2025-05-12 16:02:24,338 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=bcd0304632bc409cbbdf5ceb7ee33714
2025-05-12 16:02:24,344 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=bcd0304632bc409cbbdf5ceb7ee33714 "HTTP/1.1 202 Accepted"
2025-05-12 16:02:24,356 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=afcb4008fae84c9daaf70ff41b4fed38 "HTTP/1.1 202 Accepted"
2025-05-12 16:02:24,409 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=bcd0304632bc409cbbdf5ceb7ee33714 "HTTP/1.1 202 Accepted"
2025-05-12 16:02:24,410 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=afcb4008fae84c9daaf70ff41b4fed38 "HTTP/1.1 202 Accepted"
2025-05-12 16:02:24,461 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=bcd0304632bc409cbbdf5ceb7ee33714 "HTTP/1.1 202 Accepted"
2025-05-12 16:02:24,982 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:02:37,223 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:02:37,931 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:02:37,932 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=a79c407999db4f4ab0a72238da63fa6a
2025-05-12 16:02:37,932 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=a79c407999db4f4ab0a72238da63fa6a
2025-05-12 16:02:37,938 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a79c407999db4f4ab0a72238da63fa6a "HTTP/1.1 202 Accepted"
2025-05-12 16:02:37,939 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:02:38,520 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:02:38,521 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=a7e808c5f3f34429b8c5b2380d5902ea
2025-05-12 16:02:38,522 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=a7e808c5f3f34429b8c5b2380d5902ea
2025-05-12 16:02:38,528 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=a7e808c5f3f34429b8c5b2380d5902ea "HTTP/1.1 202 Accepted"
2025-05-12 16:02:38,548 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a79c407999db4f4ab0a72238da63fa6a "HTTP/1.1 202 Accepted"
2025-05-12 16:02:38,579 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=a7e808c5f3f34429b8c5b2380d5902ea "HTTP/1.1 202 Accepted"
2025-05-12 16:02:38,600 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a79c407999db4f4ab0a72238da63fa6a "HTTP/1.1 202 Accepted"
2025-05-12 16:02:38,641 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=a7e808c5f3f34429b8c5b2380d5902ea "HTTP/1.1 202 Accepted"
2025-05-12 16:02:39,191 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:02:50,321 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:02:50,832 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:02:50,833 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=d5f78c29866546b49897e401fffa8b39
2025-05-12 16:02:50,833 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=d5f78c29866546b49897e401fffa8b39
2025-05-12 16:02:50,838 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d5f78c29866546b49897e401fffa8b39 "HTTP/1.1 202 Accepted"
2025-05-12 16:02:50,840 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:02:51,410 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:02:51,411 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=7dd20c95174a417fae6fa5b6ef0b59b5
2025-05-12 16:02:51,411 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=7dd20c95174a417fae6fa5b6ef0b59b5
2025-05-12 16:02:51,413 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d5f78c29866546b49897e401fffa8b39 "HTTP/1.1 202 Accepted"
2025-05-12 16:02:51,418 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=7dd20c95174a417fae6fa5b6ef0b59b5 "HTTP/1.1 202 Accepted"
2025-05-12 16:02:51,475 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=7dd20c95174a417fae6fa5b6ef0b59b5 "HTTP/1.1 202 Accepted"
2025-05-12 16:02:51,476 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d5f78c29866546b49897e401fffa8b39 "HTTP/1.1 202 Accepted"
2025-05-12 16:02:51,527 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=7dd20c95174a417fae6fa5b6ef0b59b5 "HTTP/1.1 202 Accepted"
2025-05-12 16:02:51,985 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:02:59,681 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:03:00,218 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:03:00,219 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=a689b5de793549b583770858fad87e45
2025-05-12 16:03:00,219 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=a689b5de793549b583770858fad87e45
2025-05-12 16:03:00,224 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a689b5de793549b583770858fad87e45 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:00,226 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:03:00,762 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:03:00,763 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=af61115c8d234e0abe4ae00212740aef
2025-05-12 16:03:00,763 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=af61115c8d234e0abe4ae00212740aef
2025-05-12 16:03:00,769 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=af61115c8d234e0abe4ae00212740aef "HTTP/1.1 202 Accepted"
2025-05-12 16:03:00,776 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a689b5de793549b583770858fad87e45 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:00,819 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=af61115c8d234e0abe4ae00212740aef "HTTP/1.1 202 Accepted"
2025-05-12 16:03:00,829 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a689b5de793549b583770858fad87e45 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:00,871 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=af61115c8d234e0abe4ae00212740aef "HTTP/1.1 202 Accepted"
2025-05-12 16:03:01,535 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:03:01,592 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a689b5de793549b583770858fad87e45 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:01,644 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=af61115c8d234e0abe4ae00212740aef "HTTP/1.1 202 Accepted"
2025-05-12 16:03:01,696 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=af61115c8d234e0abe4ae00212740aef "HTTP/1.1 202 Accepted"
2025-05-12 16:03:01,748 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=af61115c8d234e0abe4ae00212740aef "HTTP/1.1 202 Accepted"
2025-05-12 16:03:01,749 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a689b5de793549b583770858fad87e45 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:02,298 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:03:02,354 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a689b5de793549b583770858fad87e45 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:02,406 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=af61115c8d234e0abe4ae00212740aef "HTTP/1.1 202 Accepted"
2025-05-12 16:03:02,469 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=af61115c8d234e0abe4ae00212740aef "HTTP/1.1 202 Accepted"
2025-05-12 16:03:02,521 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=af61115c8d234e0abe4ae00212740aef "HTTP/1.1 202 Accepted"
2025-05-12 16:03:02,522 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a689b5de793549b583770858fad87e45 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:03,054 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:03:09,352 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:03:09,907 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:03:09,907 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=de67276d8cd44806953f66e6c03536e1
2025-05-12 16:03:09,908 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=de67276d8cd44806953f66e6c03536e1
2025-05-12 16:03:09,914 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=de67276d8cd44806953f66e6c03536e1 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:09,915 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:03:10,475 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:03:10,476 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=85314c859b63401d976715eb81ca1aca
2025-05-12 16:03:10,477 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=85314c859b63401d976715eb81ca1aca
2025-05-12 16:03:10,484 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=85314c859b63401d976715eb81ca1aca "HTTP/1.1 202 Accepted"
2025-05-12 16:03:10,509 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=de67276d8cd44806953f66e6c03536e1 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:10,540 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=85314c859b63401d976715eb81ca1aca "HTTP/1.1 202 Accepted"
2025-05-12 16:03:10,572 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=de67276d8cd44806953f66e6c03536e1 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:10,592 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=85314c859b63401d976715eb81ca1aca "HTTP/1.1 202 Accepted"
2025-05-12 16:03:11,219 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:03:15,779 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:03:16,478 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:03:16,479 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=ef8a9e4cc37a482c896a18c4b65c70da
2025-05-12 16:03:16,479 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=ef8a9e4cc37a482c896a18c4b65c70da
2025-05-12 16:03:16,485 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=ef8a9e4cc37a482c896a18c4b65c70da "HTTP/1.1 202 Accepted"
2025-05-12 16:03:16,487 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:03:17,114 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:03:17,114 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=d96856d8d86544539669c56b0ae73aff
2025-05-12 16:03:17,115 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=d96856d8d86544539669c56b0ae73aff
2025-05-12 16:03:17,127 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d96856d8d86544539669c56b0ae73aff "HTTP/1.1 202 Accepted"
2025-05-12 16:03:17,135 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=ef8a9e4cc37a482c896a18c4b65c70da "HTTP/1.1 202 Accepted"
2025-05-12 16:03:17,188 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d96856d8d86544539669c56b0ae73aff "HTTP/1.1 202 Accepted"
2025-05-12 16:03:17,189 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=ef8a9e4cc37a482c896a18c4b65c70da "HTTP/1.1 202 Accepted"
2025-05-12 16:03:17,250 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d96856d8d86544539669c56b0ae73aff "HTTP/1.1 202 Accepted"
2025-05-12 16:03:17,865 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:03:17,928 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=ef8a9e4cc37a482c896a18c4b65c70da "HTTP/1.1 202 Accepted"
2025-05-12 16:03:17,980 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d96856d8d86544539669c56b0ae73aff "HTTP/1.1 202 Accepted"
2025-05-12 16:03:18,032 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d96856d8d86544539669c56b0ae73aff "HTTP/1.1 202 Accepted"
2025-05-12 16:03:18,856 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d96856d8d86544539669c56b0ae73aff "HTTP/1.1 202 Accepted"
2025-05-12 16:03:18,857 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=ef8a9e4cc37a482c896a18c4b65c70da "HTTP/1.1 202 Accepted"
2025-05-12 16:03:19,341 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:03:32,075 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:03:32,772 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:03:32,773 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=c8aac37c0cf04c00bcb8a405cd444133
2025-05-12 16:03:32,774 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=c8aac37c0cf04c00bcb8a405cd444133
2025-05-12 16:03:32,779 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=c8aac37c0cf04c00bcb8a405cd444133 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:32,780 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:03:33,294 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:03:33,295 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=743bcc10542d425b8f53c47550168537
2025-05-12 16:03:33,296 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=743bcc10542d425b8f53c47550168537
2025-05-12 16:03:33,302 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=743bcc10542d425b8f53c47550168537 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:33,320 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=c8aac37c0cf04c00bcb8a405cd444133 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:33,351 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=743bcc10542d425b8f53c47550168537 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:33,372 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=c8aac37c0cf04c00bcb8a405cd444133 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:33,414 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=743bcc10542d425b8f53c47550168537 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:33,994 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:03:34,628 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=c8aac37c0cf04c00bcb8a405cd444133 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:34,680 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=743bcc10542d425b8f53c47550168537 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:34,732 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=743bcc10542d425b8f53c47550168537 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:35,109 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=743bcc10542d425b8f53c47550168537 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:35,120 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=c8aac37c0cf04c00bcb8a405cd444133 "HTTP/1.1 202 Accepted"
2025-05-12 16:03:35,584 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:03:50,900 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:03:51,449 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:03:51,450 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=85cc8c66796d4ec4912d7ff4941baaae
2025-05-12 16:03:51,451 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=85cc8c66796d4ec4912d7ff4941baaae
2025-05-12 16:03:51,456 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=85cc8c66796d4ec4912d7ff4941baaae "HTTP/1.1 202 Accepted"
2025-05-12 16:03:51,457 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:03:51,997 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:03:51,997 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=21ef87353f5d4205a2c90a49fbc71c1b
2025-05-12 16:03:51,998 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=21ef87353f5d4205a2c90a49fbc71c1b
2025-05-12 16:03:52,006 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=21ef87353f5d4205a2c90a49fbc71c1b "HTTP/1.1 202 Accepted"
2025-05-12 16:03:52,018 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=85cc8c66796d4ec4912d7ff4941baaae "HTTP/1.1 202 Accepted"
2025-05-12 16:03:52,070 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=21ef87353f5d4205a2c90a49fbc71c1b "HTTP/1.1 202 Accepted"
2025-05-12 16:03:52,080 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=85cc8c66796d4ec4912d7ff4941baaae "HTTP/1.1 202 Accepted"
2025-05-12 16:03:52,132 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=21ef87353f5d4205a2c90a49fbc71c1b "HTTP/1.1 202 Accepted"
2025-05-12 16:03:52,681 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:04:30,584 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:04:31,248 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:04:31,250 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=99ea90c300224e0081ccb6cd9cb03090
2025-05-12 16:04:31,250 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=99ea90c300224e0081ccb6cd9cb03090
2025-05-12 16:04:31,259 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=99ea90c300224e0081ccb6cd9cb03090 "HTTP/1.1 202 Accepted"
2025-05-12 16:04:31,262 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:04:32,164 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:04:32,165 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=6080c359d26246ff88d6b009fc702886
2025-05-12 16:04:32,165 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=6080c359d26246ff88d6b009fc702886
2025-05-12 16:04:32,171 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6080c359d26246ff88d6b009fc702886 "HTTP/1.1 202 Accepted"
2025-05-12 16:04:32,175 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=99ea90c300224e0081ccb6cd9cb03090 "HTTP/1.1 202 Accepted"
2025-05-12 16:04:32,226 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=99ea90c300224e0081ccb6cd9cb03090 "HTTP/1.1 202 Accepted"
2025-05-12 16:04:32,227 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6080c359d26246ff88d6b009fc702886 "HTTP/1.1 202 Accepted"
2025-05-12 16:04:32,278 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6080c359d26246ff88d6b009fc702886 "HTTP/1.1 202 Accepted"
2025-05-12 16:04:32,848 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:04:32,906 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=99ea90c300224e0081ccb6cd9cb03090 "HTTP/1.1 202 Accepted"
2025-05-12 16:04:32,968 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6080c359d26246ff88d6b009fc702886 "HTTP/1.1 202 Accepted"
2025-05-12 16:04:33,031 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6080c359d26246ff88d6b009fc702886 "HTTP/1.1 202 Accepted"
2025-05-12 16:04:33,083 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6080c359d26246ff88d6b009fc702886 "HTTP/1.1 202 Accepted"
2025-05-12 16:04:33,084 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=99ea90c300224e0081ccb6cd9cb03090 "HTTP/1.1 202 Accepted"
2025-05-12 16:04:33,540 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:06:57,278 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 16:06:58,693 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 16:06:58,695 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 16:06:58,701 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:06:58,724 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:07:13,726 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 16:07:14,989 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 16:07:14,990 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 16:07:14,996 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:07:15,019 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:07:29,101 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 16:07:30,442 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 16:07:30,444 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 16:07:30,450 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:07:30,473 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:07:48,500 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 16:07:49,990 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 16:07:49,992 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 16:07:49,998 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:07:50,024 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:07:54,679 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 16:07:56,039 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 16:07:56,042 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 16:07:56,053 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:07:56,073 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:09:45,118 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:09:45,883 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:09:45,884 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=daa538d534484b14a275b87065be6a3b
2025-05-12 16:09:45,885 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=daa538d534484b14a275b87065be6a3b
2025-05-12 16:09:45,898 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=daa538d534484b14a275b87065be6a3b "HTTP/1.1 202 Accepted"
2025-05-12 16:09:45,901 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:09:46,678 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:09:46,679 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=6b5f676db2224963a2ad07e13ed7b76c
2025-05-12 16:09:46,680 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=6b5f676db2224963a2ad07e13ed7b76c
2025-05-12 16:09:46,682 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=daa538d534484b14a275b87065be6a3b "HTTP/1.1 202 Accepted"
2025-05-12 16:09:46,692 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6b5f676db2224963a2ad07e13ed7b76c "HTTP/1.1 202 Accepted"
2025-05-12 16:09:46,756 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=daa538d534484b14a275b87065be6a3b "HTTP/1.1 202 Accepted"
2025-05-12 16:09:46,758 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6b5f676db2224963a2ad07e13ed7b76c "HTTP/1.1 202 Accepted"
2025-05-12 16:09:46,818 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6b5f676db2224963a2ad07e13ed7b76c "HTTP/1.1 202 Accepted"
2025-05-12 16:09:47,668 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:09:47,728 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=daa538d534484b14a275b87065be6a3b "HTTP/1.1 202 Accepted"
2025-05-12 16:09:47,780 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6b5f676db2224963a2ad07e13ed7b76c "HTTP/1.1 202 Accepted"
2025-05-12 16:09:47,832 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6b5f676db2224963a2ad07e13ed7b76c "HTTP/1.1 202 Accepted"
2025-05-12 16:09:47,884 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6b5f676db2224963a2ad07e13ed7b76c "HTTP/1.1 202 Accepted"
2025-05-12 16:09:47,886 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=daa538d534484b14a275b87065be6a3b "HTTP/1.1 202 Accepted"
2025-05-12 16:09:48,422 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:09:54,308 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:09:54,942 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:09:54,943 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=d033965eff7a45d9aec77adc11a155f3
2025-05-12 16:09:54,944 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=d033965eff7a45d9aec77adc11a155f3
2025-05-12 16:09:54,949 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d033965eff7a45d9aec77adc11a155f3 "HTTP/1.1 202 Accepted"
2025-05-12 16:09:54,951 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:09:55,613 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:09:55,615 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=cac4e0fb5ff4470190d87afc8937bdca
2025-05-12 16:09:55,616 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=cac4e0fb5ff4470190d87afc8937bdca
2025-05-12 16:09:55,623 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=cac4e0fb5ff4470190d87afc8937bdca "HTTP/1.1 202 Accepted"
2025-05-12 16:09:55,637 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d033965eff7a45d9aec77adc11a155f3 "HTTP/1.1 202 Accepted"
2025-05-12 16:09:55,689 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=cac4e0fb5ff4470190d87afc8937bdca "HTTP/1.1 202 Accepted"
2025-05-12 16:09:55,691 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d033965eff7a45d9aec77adc11a155f3 "HTTP/1.1 202 Accepted"
2025-05-12 16:09:55,752 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=cac4e0fb5ff4470190d87afc8937bdca "HTTP/1.1 202 Accepted"
2025-05-12 16:09:56,467 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:09:56,526 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d033965eff7a45d9aec77adc11a155f3 "HTTP/1.1 202 Accepted"
2025-05-12 16:09:56,578 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=cac4e0fb5ff4470190d87afc8937bdca "HTTP/1.1 202 Accepted"
2025-05-12 16:09:56,640 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=cac4e0fb5ff4470190d87afc8937bdca "HTTP/1.1 202 Accepted"
2025-05-12 16:09:56,692 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=cac4e0fb5ff4470190d87afc8937bdca "HTTP/1.1 202 Accepted"
2025-05-12 16:09:56,693 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d033965eff7a45d9aec77adc11a155f3 "HTTP/1.1 202 Accepted"
2025-05-12 16:09:57,198 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:10:08,091 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:10:08,904 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:10:08,906 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=0890ef6286c3471e8ff9a590fac14bc9
2025-05-12 16:10:08,907 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=0890ef6286c3471e8ff9a590fac14bc9
2025-05-12 16:10:08,914 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=0890ef6286c3471e8ff9a590fac14bc9 "HTTP/1.1 202 Accepted"
2025-05-12 16:10:08,915 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:10:09,688 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:10:09,690 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=285cbfab57094c9980c316e307a67d92
2025-05-12 16:10:09,690 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=285cbfab57094c9980c316e307a67d92
2025-05-12 16:10:09,699 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=285cbfab57094c9980c316e307a67d92 "HTTP/1.1 202 Accepted"
2025-05-12 16:10:09,708 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=0890ef6286c3471e8ff9a590fac14bc9 "HTTP/1.1 202 Accepted"
2025-05-12 16:10:09,761 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=285cbfab57094c9980c316e307a67d92 "HTTP/1.1 202 Accepted"
2025-05-12 16:10:09,771 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=0890ef6286c3471e8ff9a590fac14bc9 "HTTP/1.1 202 Accepted"
2025-05-12 16:10:09,812 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=285cbfab57094c9980c316e307a67d92 "HTTP/1.1 202 Accepted"
2025-05-12 16:10:10,393 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:10:19,666 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:10:22,953 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:10:22,954 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=2909a9b152bd41dab5839abd8dff380a
2025-05-12 16:10:22,954 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=2909a9b152bd41dab5839abd8dff380a
2025-05-12 16:10:22,963 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=2909a9b152bd41dab5839abd8dff380a "HTTP/1.1 202 Accepted"
2025-05-12 16:10:22,965 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:10:23,857 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:10:23,859 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=2f4b47c3737c4e8da6113a16f18e5190
2025-05-12 16:10:23,860 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=2f4b47c3737c4e8da6113a16f18e5190
2025-05-12 16:10:23,869 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2f4b47c3737c4e8da6113a16f18e5190 "HTTP/1.1 202 Accepted"
2025-05-12 16:10:23,878 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=2909a9b152bd41dab5839abd8dff380a "HTTP/1.1 202 Accepted"
2025-05-12 16:10:23,931 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2f4b47c3737c4e8da6113a16f18e5190 "HTTP/1.1 202 Accepted"
2025-05-12 16:10:23,933 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=2909a9b152bd41dab5839abd8dff380a "HTTP/1.1 202 Accepted"
2025-05-12 16:10:23,983 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2f4b47c3737c4e8da6113a16f18e5190 "HTTP/1.1 202 Accepted"
2025-05-12 16:10:24,540 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:10:55,184 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:10:56,537 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:10:56,540 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=6c84bafa7c2c411f9fc025c1233ebf71
2025-05-12 16:10:56,546 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=6c84bafa7c2c411f9fc025c1233ebf71
2025-05-12 16:10:56,561 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=6c84bafa7c2c411f9fc025c1233ebf71 "HTTP/1.1 202 Accepted"
2025-05-12 16:10:56,565 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:10:57,620 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:10:57,621 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=84ff3c94defe4ce1a4bf74edc113c371
2025-05-12 16:10:57,622 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=84ff3c94defe4ce1a4bf74edc113c371
2025-05-12 16:10:57,629 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=84ff3c94defe4ce1a4bf74edc113c371 "HTTP/1.1 202 Accepted"
2025-05-12 16:10:57,636 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=6c84bafa7c2c411f9fc025c1233ebf71 "HTTP/1.1 202 Accepted"
2025-05-12 16:10:57,678 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=84ff3c94defe4ce1a4bf74edc113c371 "HTTP/1.1 202 Accepted"
2025-05-12 16:10:57,700 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=6c84bafa7c2c411f9fc025c1233ebf71 "HTTP/1.1 202 Accepted"
2025-05-12 16:10:57,730 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=84ff3c94defe4ce1a4bf74edc113c371 "HTTP/1.1 202 Accepted"
2025-05-12 16:10:58,431 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:11:07,521 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:11:08,136 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:11:08,137 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=0f6c0d8330124097aeffa3854853d1af
2025-05-12 16:11:08,138 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=0f6c0d8330124097aeffa3854853d1af
2025-05-12 16:11:08,164 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=0f6c0d8330124097aeffa3854853d1af "HTTP/1.1 202 Accepted"
2025-05-12 16:11:08,166 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:11:08,779 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:11:08,780 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=f6e27f73258c4cfbb6cf59d56a0ed91d
2025-05-12 16:11:08,780 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=f6e27f73258c4cfbb6cf59d56a0ed91d
2025-05-12 16:11:08,787 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f6e27f73258c4cfbb6cf59d56a0ed91d "HTTP/1.1 202 Accepted"
2025-05-12 16:11:08,806 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=0f6c0d8330124097aeffa3854853d1af "HTTP/1.1 202 Accepted"
2025-05-12 16:11:08,848 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f6e27f73258c4cfbb6cf59d56a0ed91d "HTTP/1.1 202 Accepted"
2025-05-12 16:11:08,858 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=0f6c0d8330124097aeffa3854853d1af "HTTP/1.1 202 Accepted"
2025-05-12 16:11:08,900 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f6e27f73258c4cfbb6cf59d56a0ed91d "HTTP/1.1 202 Accepted"
2025-05-12 16:11:09,455 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:11:29,383 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:11:30,003 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:11:30,005 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=70d839f532294b3e91d525778346f79c
2025-05-12 16:11:30,006 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=70d839f532294b3e91d525778346f79c
2025-05-12 16:11:30,013 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=70d839f532294b3e91d525778346f79c "HTTP/1.1 202 Accepted"
2025-05-12 16:11:30,015 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:11:30,652 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:11:30,654 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=2240efa2e7484edbb2f2b31b8ebcc453
2025-05-12 16:11:30,654 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=2240efa2e7484edbb2f2b31b8ebcc453
2025-05-12 16:11:30,663 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2240efa2e7484edbb2f2b31b8ebcc453 "HTTP/1.1 202 Accepted"
2025-05-12 16:11:30,673 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=70d839f532294b3e91d525778346f79c "HTTP/1.1 202 Accepted"
2025-05-12 16:11:30,725 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=70d839f532294b3e91d525778346f79c "HTTP/1.1 202 Accepted"
2025-05-12 16:11:30,727 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2240efa2e7484edbb2f2b31b8ebcc453 "HTTP/1.1 202 Accepted"
2025-05-12 16:11:30,777 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2240efa2e7484edbb2f2b31b8ebcc453 "HTTP/1.1 202 Accepted"
2025-05-12 16:11:32,040 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:11:36,049 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=70d839f532294b3e91d525778346f79c "HTTP/1.1 202 Accepted"
2025-05-12 16:11:36,114 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=70d839f532294b3e91d525778346f79c "HTTP/1.1 202 Accepted"
2025-05-12 16:11:49,013 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:11:49,610 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:11:49,611 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=7b50a3154c6d4149942cf6ddf08add77
2025-05-12 16:11:49,612 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=7b50a3154c6d4149942cf6ddf08add77
2025-05-12 16:11:49,618 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=7b50a3154c6d4149942cf6ddf08add77 "HTTP/1.1 202 Accepted"
2025-05-12 16:11:49,620 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:11:50,290 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:11:50,292 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=e44bbe37a2454494a1a8cca48cfe48d5
2025-05-12 16:11:50,293 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=e44bbe37a2454494a1a8cca48cfe48d5
2025-05-12 16:11:50,305 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e44bbe37a2454494a1a8cca48cfe48d5 "HTTP/1.1 202 Accepted"
2025-05-12 16:11:50,309 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=7b50a3154c6d4149942cf6ddf08add77 "HTTP/1.1 202 Accepted"
2025-05-12 16:11:50,361 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e44bbe37a2454494a1a8cca48cfe48d5 "HTTP/1.1 202 Accepted"
2025-05-12 16:11:50,365 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=7b50a3154c6d4149942cf6ddf08add77 "HTTP/1.1 202 Accepted"
2025-05-12 16:11:50,423 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e44bbe37a2454494a1a8cca48cfe48d5 "HTTP/1.1 202 Accepted"
2025-05-12 16:11:50,744 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:12:31,878 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:12:33,045 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:12:33,047 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=d324942eda9742dc96f5d4ed702e33fd
2025-05-12 16:12:33,047 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=d324942eda9742dc96f5d4ed702e33fd
2025-05-12 16:12:33,057 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d324942eda9742dc96f5d4ed702e33fd "HTTP/1.1 202 Accepted"
2025-05-12 16:12:33,060 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:12:33,887 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:12:33,888 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=eed8c50f2d1547f49d2ce2d16de8cb68
2025-05-12 16:12:33,889 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=eed8c50f2d1547f49d2ce2d16de8cb68
2025-05-12 16:12:33,898 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=eed8c50f2d1547f49d2ce2d16de8cb68 "HTTP/1.1 202 Accepted"
2025-05-12 16:12:33,915 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d324942eda9742dc96f5d4ed702e33fd "HTTP/1.1 202 Accepted"
2025-05-12 16:12:33,968 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=eed8c50f2d1547f49d2ce2d16de8cb68 "HTTP/1.1 202 Accepted"
2025-05-12 16:12:33,969 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d324942eda9742dc96f5d4ed702e33fd "HTTP/1.1 202 Accepted"
2025-05-12 16:12:34,019 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=eed8c50f2d1547f49d2ce2d16de8cb68 "HTTP/1.1 202 Accepted"
2025-05-12 16:12:34,282 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:12:38,102 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d324942eda9742dc96f5d4ed702e33fd "HTTP/1.1 202 Accepted"
2025-05-12 16:12:38,164 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d324942eda9742dc96f5d4ed702e33fd "HTTP/1.1 202 Accepted"
2025-05-12 16:12:44,216 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:12:44,969 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:12:44,970 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=d153fde61b7242fb81074faed2533041
2025-05-12 16:12:44,971 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=d153fde61b7242fb81074faed2533041
2025-05-12 16:12:44,980 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d153fde61b7242fb81074faed2533041 "HTTP/1.1 202 Accepted"
2025-05-12 16:12:44,982 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:12:45,659 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:12:45,660 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=b37e97bbc5494aed9c141b687cb6762d
2025-05-12 16:12:45,660 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=b37e97bbc5494aed9c141b687cb6762d
2025-05-12 16:12:45,666 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=b37e97bbc5494aed9c141b687cb6762d "HTTP/1.1 202 Accepted"
2025-05-12 16:12:45,674 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d153fde61b7242fb81074faed2533041 "HTTP/1.1 202 Accepted"
2025-05-12 16:12:45,716 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=b37e97bbc5494aed9c141b687cb6762d "HTTP/1.1 202 Accepted"
2025-05-12 16:12:45,737 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d153fde61b7242fb81074faed2533041 "HTTP/1.1 202 Accepted"
2025-05-12 16:12:45,768 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=b37e97bbc5494aed9c141b687cb6762d "HTTP/1.1 202 Accepted"
2025-05-12 16:12:46,035 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:12:57,950 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d153fde61b7242fb81074faed2533041 "HTTP/1.1 202 Accepted"
2025-05-12 16:12:57,962 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=b37e97bbc5494aed9c141b687cb6762d "HTTP/1.1 202 Accepted"
2025-05-12 16:12:58,015 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=b37e97bbc5494aed9c141b687cb6762d "HTTP/1.1 202 Accepted"
2025-05-12 16:13:03,647 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:13:04,337 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:13:04,338 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=b4d082a8e5924f5cac71837192d49f58
2025-05-12 16:13:04,339 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=b4d082a8e5924f5cac71837192d49f58
2025-05-12 16:13:04,347 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b4d082a8e5924f5cac71837192d49f58 "HTTP/1.1 202 Accepted"
2025-05-12 16:13:04,349 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:13:05,001 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:13:05,003 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=203211df6ec440f2bd988c2c6a491be2
2025-05-12 16:13:05,004 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=203211df6ec440f2bd988c2c6a491be2
2025-05-12 16:13:05,014 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=203211df6ec440f2bd988c2c6a491be2 "HTTP/1.1 202 Accepted"
2025-05-12 16:13:05,023 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b4d082a8e5924f5cac71837192d49f58 "HTTP/1.1 202 Accepted"
2025-05-12 16:13:05,076 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=203211df6ec440f2bd988c2c6a491be2 "HTTP/1.1 202 Accepted"
2025-05-12 16:13:05,077 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b4d082a8e5924f5cac71837192d49f58 "HTTP/1.1 202 Accepted"
2025-05-12 16:13:05,148 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=203211df6ec440f2bd988c2c6a491be2 "HTTP/1.1 202 Accepted"
2025-05-12 16:13:05,633 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:13:34,891 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:13:35,612 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:13:35,613 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=e14c737994c74c9ba40cf52afc1d62cd
2025-05-12 16:13:35,614 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=e14c737994c74c9ba40cf52afc1d62cd
2025-05-12 16:13:35,623 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e14c737994c74c9ba40cf52afc1d62cd "HTTP/1.1 202 Accepted"
2025-05-12 16:13:35,625 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:13:36,301 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:13:36,302 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=2933b60018bd45c2911e4485a679381c
2025-05-12 16:13:36,303 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=2933b60018bd45c2911e4485a679381c
2025-05-12 16:13:36,309 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2933b60018bd45c2911e4485a679381c "HTTP/1.1 202 Accepted"
2025-05-12 16:13:36,321 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e14c737994c74c9ba40cf52afc1d62cd "HTTP/1.1 202 Accepted"
2025-05-12 16:13:36,373 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2933b60018bd45c2911e4485a679381c "HTTP/1.1 202 Accepted"
2025-05-12 16:13:36,375 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e14c737994c74c9ba40cf52afc1d62cd "HTTP/1.1 202 Accepted"
2025-05-12 16:13:36,425 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2933b60018bd45c2911e4485a679381c "HTTP/1.1 202 Accepted"
2025-05-12 16:13:36,669 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:13:40,185 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e14c737994c74c9ba40cf52afc1d62cd "HTTP/1.1 202 Accepted"
2025-05-12 16:13:40,248 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e14c737994c74c9ba40cf52afc1d62cd "HTTP/1.1 202 Accepted"
2025-05-12 16:14:23,106 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 16:14:23,826 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:14:23,827 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=fe97cf17e4fb4dd4b3a657d9c3edd16f
2025-05-12 16:14:23,828 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=fe97cf17e4fb4dd4b3a657d9c3edd16f
2025-05-12 16:14:23,836 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=fe97cf17e4fb4dd4b3a657d9c3edd16f "HTTP/1.1 202 Accepted"
2025-05-12 16:14:23,838 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 16:14:24,445 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:14:24,446 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=4a89744f946d46868932a587d0904059
2025-05-12 16:14:24,447 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=4a89744f946d46868932a587d0904059
2025-05-12 16:14:24,454 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=4a89744f946d46868932a587d0904059 "HTTP/1.1 202 Accepted"
2025-05-12 16:14:24,467 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=fe97cf17e4fb4dd4b3a657d9c3edd16f "HTTP/1.1 202 Accepted"
2025-05-12 16:14:24,509 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=4a89744f946d46868932a587d0904059 "HTTP/1.1 202 Accepted"
2025-05-12 16:14:24,520 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=fe97cf17e4fb4dd4b3a657d9c3edd16f "HTTP/1.1 202 Accepted"
2025-05-12 16:14:24,562 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=4a89744f946d46868932a587d0904059 "HTTP/1.1 202 Accepted"
2025-05-12 16:14:25,076 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 16:14:41,732 INFO HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:batchEmbedContents "HTTP/1.1 200 OK"
2025-05-12 16:14:43,201 INFO HTTP Request: POST https://gttwzegcmextyljehehj.supabase.co/rest/v1/rpc/match_documents_768 "HTTP/2 200 OK"
2025-05-12 16:15:00,923 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 16:15:02,542 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 16:15:02,545 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 16:15:02,550 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:15:02,600 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:17:01,620 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 16:17:03,104 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 16:17:03,106 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 16:17:03,112 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:17:03,157 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:21:05,841 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 16:21:06,945 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 16:21:06,947 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 16:21:06,953 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:21:06,983 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:22:11,298 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 16:22:12,415 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 16:22:12,416 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 16:22:12,421 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:22:12,441 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:22:27,265 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 16:22:28,316 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 16:22:28,317 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 16:22:28,321 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 16:22:28,341 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 16:22:40,804 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 17:52:50,024 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 17:52:51,161 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 17:52:51,163 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 17:52:51,174 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 17:52:51,204 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 17:55:06,945 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 17:55:08,474 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 17:55:08,475 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 17:55:08,480 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 17:55:08,501 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 17:56:14,202 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 17:56:15,230 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 17:56:15,232 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 17:56:15,237 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 17:56:15,266 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 17:57:09,570 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 17:57:10,752 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 17:57:10,753 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 17:57:10,758 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 17:57:10,782 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 17:57:20,012 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 17:57:25,690 INFO Creating new collection: mem0_memories768
2025-05-12 17:57:27,357 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 17:57:31,677 INFO Creating new collection: mem0migrations
2025-05-12 17:57:33,454 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 17:57:34,041 INFO Inserting 1 vectors into collection mem0migrations
2025-05-12 17:57:34,622 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 17:57:34,622 INFO Retrieving memories for user_id=user-shpkzdfu, query='Check'
2025-05-12 17:57:37,500 INFO Retrieved memory for user_id=user-shpkzdfu, query='Check': {'results': []}
2025-05-12 17:57:37,516 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 17:57:38,399 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 17:57:38,400 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=9fe0f7b8ad8c4abd8b69152ce030f377
2025-05-12 17:57:38,401 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=9fe0f7b8ad8c4abd8b69152ce030f377
2025-05-12 17:57:38,416 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9fe0f7b8ad8c4abd8b69152ce030f377 "HTTP/1.1 202 Accepted"
2025-05-12 17:57:38,425 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 17:57:39,101 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 17:57:39,103 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=f09970373a5a44d99d96ce21fc74b344
2025-05-12 17:57:39,103 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=f09970373a5a44d99d96ce21fc74b344
2025-05-12 17:57:39,116 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f09970373a5a44d99d96ce21fc74b344 "HTTP/1.1 202 Accepted"
2025-05-12 17:57:39,127 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9fe0f7b8ad8c4abd8b69152ce030f377 "HTTP/1.1 202 Accepted"
2025-05-12 17:57:39,199 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f09970373a5a44d99d96ce21fc74b344 "HTTP/1.1 202 Accepted"
2025-05-12 17:57:39,201 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9fe0f7b8ad8c4abd8b69152ce030f377 "HTTP/1.1 202 Accepted"
2025-05-12 17:57:39,252 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f09970373a5a44d99d96ce21fc74b344 "HTTP/1.1 202 Accepted"
2025-05-12 17:57:40,028 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 17:57:40,096 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9fe0f7b8ad8c4abd8b69152ce030f377 "HTTP/1.1 202 Accepted"
2025-05-12 17:57:40,168 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9fe0f7b8ad8c4abd8b69152ce030f377 "HTTP/1.1 202 Accepted"
2025-05-12 17:57:40,231 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9fe0f7b8ad8c4abd8b69152ce030f377 "HTTP/1.1 202 Accepted"
2025-05-12 17:57:40,241 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f09970373a5a44d99d96ce21fc74b344 "HTTP/1.1 202 Accepted"
2025-05-12 17:57:40,643 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 17:57:45,404 INFO Creating new collection: mem0_memories768
2025-05-12 17:57:47,283 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 17:57:52,318 INFO Creating new collection: mem0migrations
2025-05-12 17:57:54,427 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 17:57:55,178 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 17:57:55,178 INFO Adding memory for user_id=user-shpkzdfu
2025-05-12 17:57:56,635 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 17:57:57,207 INFO Total existing memories: 0
2025-05-12 17:57:57,273 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 17:58:32,156 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 17:58:33,353 INFO Creating new collection: mem0_memories768
2025-05-12 17:58:33,864 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 17:58:35,930 INFO Creating new collection: mem0migrations
2025-05-12 17:58:36,449 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 17:58:36,653 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 17:58:36,654 INFO Retrieving memories for user_id=user-shpkzdfu, query='Which llm are you ?'
2025-05-12 17:58:38,203 INFO Retrieved memory for user_id=user-shpkzdfu, query='Which llm are you ?': {'results': []}
2025-05-12 17:58:38,218 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 17:58:39,010 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 17:58:39,010 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=a3e8046e63ca49abb5ec3881d9be9511
2025-05-12 17:58:39,011 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=a3e8046e63ca49abb5ec3881d9be9511
2025-05-12 17:58:39,022 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a3e8046e63ca49abb5ec3881d9be9511 "HTTP/1.1 202 Accepted"
2025-05-12 17:58:39,024 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 17:58:39,674 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 17:58:39,676 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=a28afa5fb40b4fd6a54365a076d3d3dd
2025-05-12 17:58:39,676 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=a28afa5fb40b4fd6a54365a076d3d3dd
2025-05-12 17:58:39,684 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=a28afa5fb40b4fd6a54365a076d3d3dd "HTTP/1.1 202 Accepted"
2025-05-12 17:58:39,699 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a3e8046e63ca49abb5ec3881d9be9511 "HTTP/1.1 202 Accepted"
2025-05-12 17:58:39,751 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=a28afa5fb40b4fd6a54365a076d3d3dd "HTTP/1.1 202 Accepted"
2025-05-12 17:58:39,762 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a3e8046e63ca49abb5ec3881d9be9511 "HTTP/1.1 202 Accepted"
2025-05-12 17:58:39,804 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=a28afa5fb40b4fd6a54365a076d3d3dd "HTTP/1.1 202 Accepted"
2025-05-12 17:58:40,289 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 17:58:41,677 INFO Creating new collection: mem0_memories768
2025-05-12 17:58:42,197 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 17:58:45,243 INFO Creating new collection: mem0migrations
2025-05-12 17:58:45,778 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 17:58:45,985 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 17:58:45,985 INFO Adding memory for user_id=user-shpkzdfu
2025-05-12 17:58:46,154 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 17:58:46,936 INFO Total existing memories: 0
2025-05-12 17:58:47,023 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 17:59:39,456 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 17:59:39,632 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 17:59:40,819 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 17:59:40,821 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=3337889150c2479894171a096391c27c
2025-05-12 17:59:40,821 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=3337889150c2479894171a096391c27c
2025-05-12 17:59:40,830 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=3337889150c2479894171a096391c27c "HTTP/1.1 202 Accepted"
2025-05-12 17:59:40,832 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 17:59:41,545 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 17:59:41,547 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=a4d2e348f65642a7be2967dd82c6bd62
2025-05-12 17:59:41,547 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=a4d2e348f65642a7be2967dd82c6bd62
2025-05-12 17:59:41,555 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=a4d2e348f65642a7be2967dd82c6bd62 "HTTP/1.1 202 Accepted"
2025-05-12 17:59:41,569 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=3337889150c2479894171a096391c27c "HTTP/1.1 202 Accepted"
2025-05-12 17:59:41,611 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=a4d2e348f65642a7be2967dd82c6bd62 "HTTP/1.1 202 Accepted"
2025-05-12 17:59:41,632 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=3337889150c2479894171a096391c27c "HTTP/1.1 202 Accepted"
2025-05-12 17:59:41,674 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=a4d2e348f65642a7be2967dd82c6bd62 "HTTP/1.1 202 Accepted"
2025-05-12 17:59:42,604 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 18:03:19,427 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:03:20,532 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:03:20,533 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:03:20,538 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:03:20,560 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:03:32,089 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:03:33,167 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:03:33,168 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:03:33,173 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:03:33,192 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:10:19,305 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:10:20,664 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:10:20,666 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:10:20,673 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:10:20,703 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:10:52,043 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:10:54,037 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:10:54,039 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:10:54,044 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:10:54,074 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:11:48,744 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:11:49,869 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:11:49,871 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:11:49,875 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:11:49,895 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:14:43,160 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:14:44,189 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:14:44,191 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:14:44,195 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:14:44,222 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:17:12,239 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:17:13,429 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:17:13,430 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:17:13,436 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:17:13,465 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:17:22,899 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 18:18:43,596 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:18:44,738 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:18:44,740 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:18:44,746 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:18:44,772 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:19:16,398 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 18:19:16,431 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 18:19:17,055 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:19:17,056 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=6954b187d5ed4826bba1170e5fe908e8
2025-05-12 18:19:17,056 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=6954b187d5ed4826bba1170e5fe908e8
2025-05-12 18:19:17,062 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=6954b187d5ed4826bba1170e5fe908e8 "HTTP/1.1 202 Accepted"
2025-05-12 18:19:17,064 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 18:19:17,618 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:19:17,619 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=6283fc139af746fbbc342d2b21bc7568
2025-05-12 18:19:17,620 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=6283fc139af746fbbc342d2b21bc7568
2025-05-12 18:19:17,626 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6283fc139af746fbbc342d2b21bc7568 "HTTP/1.1 202 Accepted"
2025-05-12 18:21:04,916 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:21:05,984 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:21:05,986 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:21:05,989 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:21:06,016 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:21:14,889 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 18:21:14,913 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 18:21:15,490 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:21:15,491 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=ddce98099a064360a12f03d9005f54a9
2025-05-12 18:21:15,491 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=ddce98099a064360a12f03d9005f54a9
2025-05-12 18:21:15,515 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=ddce98099a064360a12f03d9005f54a9 "HTTP/1.1 202 Accepted"
2025-05-12 18:21:15,516 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 18:21:16,085 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:21:16,086 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=e3d124f7a20646fe843b824354502295
2025-05-12 18:21:16,086 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=e3d124f7a20646fe843b824354502295
2025-05-12 18:21:16,091 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e3d124f7a20646fe843b824354502295 "HTTP/1.1 202 Accepted"
2025-05-12 18:21:16,109 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=ddce98099a064360a12f03d9005f54a9 "HTTP/1.1 202 Accepted"
2025-05-12 18:21:16,152 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e3d124f7a20646fe843b824354502295 "HTTP/1.1 202 Accepted"
2025-05-12 18:21:16,162 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=ddce98099a064360a12f03d9005f54a9 "HTTP/1.1 202 Accepted"
2025-05-12 18:21:16,204 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e3d124f7a20646fe843b824354502295 "HTTP/1.1 202 Accepted"
2025-05-12 18:21:17,255 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 18:21:19,980 INFO HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:batchEmbedContents "HTTP/1.1 200 OK"
2025-05-12 18:21:20,805 INFO HTTP Request: POST https://gttwzegcmextyljehehj.supabase.co/rest/v1/rpc/match_documents_768 "HTTP/2 200 OK"
2025-05-12 18:22:50,984 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:22:52,035 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:22:52,036 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:22:52,041 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:22:52,070 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:23:45,173 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 18:23:48,209 INFO Creating new collection: mem0_memories768
2025-05-12 18:23:48,695 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 18:23:50,500 INFO Creating new collection: mem0migrations
2025-05-12 18:23:50,989 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 18:23:51,137 INFO Inserting 1 vectors into collection mem0migrations
2025-05-12 18:23:51,362 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 18:23:51,362 INFO Retrieving memories for user_id=user-hgcjj28a, query='hi'
2025-05-12 18:23:52,589 INFO Retrieved memory for user_id=user-hgcjj28a, query='hi': {'results': []}
2025-05-12 18:23:52,599 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 18:23:53,329 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:23:53,331 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=f0af240aa1f240b7a8a38e50caf089fc
2025-05-12 18:23:53,331 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=f0af240aa1f240b7a8a38e50caf089fc
2025-05-12 18:23:53,339 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=f0af240aa1f240b7a8a38e50caf089fc "HTTP/1.1 202 Accepted"
2025-05-12 18:23:53,342 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 18:23:54,206 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=f0af240aa1f240b7a8a38e50caf089fc "HTTP/1.1 202 Accepted"
2025-05-12 18:23:54,218 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:23:54,219 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=124980608eac426faaeef2e65cca85f0
2025-05-12 18:23:54,220 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=124980608eac426faaeef2e65cca85f0
2025-05-12 18:23:54,228 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=124980608eac426faaeef2e65cca85f0 "HTTP/1.1 202 Accepted"
2025-05-12 18:23:54,279 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=124980608eac426faaeef2e65cca85f0 "HTTP/1.1 202 Accepted"
2025-05-12 18:23:54,290 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=f0af240aa1f240b7a8a38e50caf089fc "HTTP/1.1 202 Accepted"
2025-05-12 18:23:54,332 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=124980608eac426faaeef2e65cca85f0 "HTTP/1.1 202 Accepted"
2025-05-12 18:23:54,828 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 18:23:56,014 INFO Creating new collection: mem0_memories768
2025-05-12 18:23:56,513 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 18:23:58,297 INFO Creating new collection: mem0migrations
2025-05-12 18:23:58,786 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 18:23:58,974 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 18:23:58,974 INFO Adding memory for user_id=user-hgcjj28a
2025-05-12 18:24:00,047 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 18:24:00,662 INFO Total existing memories: 0
2025-05-12 18:24:00,752 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 18:32:21,329 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:32:22,445 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:32:22,446 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:32:22,451 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:32:22,469 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:35:43,583 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:35:44,834 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:35:44,836 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:35:44,844 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:35:44,865 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:36:12,256 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:36:13,306 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:36:13,308 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:36:13,312 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:36:13,332 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:38:41,157 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:38:42,500 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:38:42,502 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:38:42,507 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:38:42,535 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:38:43,266 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 18:38:47,252 INFO Creating new collection: mem0_memories768
2025-05-12 18:38:48,057 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 18:38:52,413 INFO Creating new collection: mem0migrations
2025-05-12 18:38:53,136 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 18:38:53,335 INFO Inserting 1 vectors into collection mem0migrations
2025-05-12 18:38:53,601 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 18:38:53,602 INFO Retrieving memories for user_id=user-hgcjj28a, query='hi'
2025-05-12 18:38:56,511 INFO Retrieved memory for user_id=user-hgcjj28a, query='hi': {'results': []}
2025-05-12 18:38:56,625 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 18:38:56,652 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 18:38:57,385 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:38:57,387 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=77db80271b8d446daa280d275e4517cf
2025-05-12 18:38:57,388 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=77db80271b8d446daa280d275e4517cf
2025-05-12 18:38:57,399 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=77db80271b8d446daa280d275e4517cf "HTTP/1.1 202 Accepted"
2025-05-12 18:38:57,402 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 18:38:58,132 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:38:58,133 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=5396559984ed46459c29eb02ac53bb1b
2025-05-12 18:38:58,134 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=5396559984ed46459c29eb02ac53bb1b
2025-05-12 18:38:58,140 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5396559984ed46459c29eb02ac53bb1b "HTTP/1.1 202 Accepted"
2025-05-12 18:38:58,162 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=77db80271b8d446daa280d275e4517cf "HTTP/1.1 202 Accepted"
2025-05-12 18:38:58,193 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5396559984ed46459c29eb02ac53bb1b "HTTP/1.1 202 Accepted"
2025-05-12 18:38:58,215 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=77db80271b8d446daa280d275e4517cf "HTTP/1.1 202 Accepted"
2025-05-12 18:38:58,257 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5396559984ed46459c29eb02ac53bb1b "HTTP/1.1 202 Accepted"
2025-05-12 18:38:58,884 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 18:38:58,956 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=77db80271b8d446daa280d275e4517cf "HTTP/1.1 202 Accepted"
2025-05-12 18:38:59,019 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5396559984ed46459c29eb02ac53bb1b "HTTP/1.1 202 Accepted"
2025-05-12 18:38:59,080 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5396559984ed46459c29eb02ac53bb1b "HTTP/1.1 202 Accepted"
2025-05-12 18:38:59,133 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5396559984ed46459c29eb02ac53bb1b "HTTP/1.1 202 Accepted"
2025-05-12 18:38:59,134 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=77db80271b8d446daa280d275e4517cf "HTTP/1.1 202 Accepted"
2025-05-12 18:38:59,609 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 18:39:01,209 INFO Creating new collection: mem0_memories768
2025-05-12 18:39:01,983 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-12 18:39:04,328 INFO Creating new collection: mem0migrations
2025-05-12 18:39:05,045 INFO Successfully created collection mem0migrations with dimension 768
2025-05-12 18:39:05,300 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-12 18:39:05,301 INFO Adding memory for user_id=user-hgcjj28a
2025-05-12 18:39:06,447 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 18:39:07,484 INFO Total existing memories: 0
2025-05-12 18:39:07,575 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 18:39:08,764 ERROR Exception while exporting Span.
Traceback (most recent call last):
  File "C:\Users\<USER>\codes\vahan_sahayak_fastapi_simple\.venv\Lib\site-packages\opentelemetry\sdk\trace\export\__init__.py", line 114, in on_end
    self.span_exporter.export((span,))
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\codes\vahan_sahayak_fastapi_simple\.venv\Lib\site-packages\opentelemetry\exporter\otlp\proto\http\trace_exporter\__init__.py", line 187, in export
    serialized_data = self._serialize_spans(spans)
  File "C:\Users\<USER>\codes\vahan_sahayak_fastapi_simple\.venv\Lib\site-packages\opentelemetry\exporter\otlp\proto\http\trace_exporter\__init__.py", line 150, in _serialize_spans
    return encode_spans(spans).SerializePartialToString()
           ~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\codes\vahan_sahayak_fastapi_simple\.venv\Lib\site-packages\opentelemetry\exporter\otlp\proto\common\_internal\trace_encoder\__init__.py", line 56, in encode_spans
    resource_spans=_encode_resource_spans(sdk_spans)
  File "C:\Users\<USER>\codes\vahan_sahayak_fastapi_simple\.venv\Lib\site-packages\opentelemetry\exporter\otlp\proto\common\_internal\trace_encoder\__init__.py", line 79, in _encode_resource_spans
    pb2_span = _encode_span(sdk_span)
  File "C:\Users\<USER>\codes\vahan_sahayak_fastapi_simple\.venv\Lib\site-packages\opentelemetry\exporter\otlp\proto\common\_internal\trace_encoder\__init__.py", line 123, in _encode_span
    kind=_SPAN_KIND_MAP[sdk_span.kind],
         ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
KeyError: None
2025-05-12 18:43:14,702 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:43:15,764 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:43:15,766 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:43:15,770 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:43:15,791 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:43:21,776 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 18:43:21,894 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 18:43:21,922 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 18:43:22,776 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:43:22,777 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=ece79fe0e8034a75a9163b01ff62f71e
2025-05-12 18:43:22,777 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=ece79fe0e8034a75a9163b01ff62f71e
2025-05-12 18:43:22,785 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=ece79fe0e8034a75a9163b01ff62f71e "HTTP/1.1 202 Accepted"
2025-05-12 18:43:22,788 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 18:43:23,528 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:43:23,530 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=511beb07bc8e404399e4d1d72984017d
2025-05-12 18:43:23,531 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=511beb07bc8e404399e4d1d72984017d
2025-05-12 18:43:23,540 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=511beb07bc8e404399e4d1d72984017d "HTTP/1.1 202 Accepted"
2025-05-12 18:43:23,546 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=ece79fe0e8034a75a9163b01ff62f71e "HTTP/1.1 202 Accepted"
2025-05-12 18:43:23,598 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=511beb07bc8e404399e4d1d72984017d "HTTP/1.1 202 Accepted"
2025-05-12 18:43:23,609 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=ece79fe0e8034a75a9163b01ff62f71e "HTTP/1.1 202 Accepted"
2025-05-12 18:43:23,662 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=511beb07bc8e404399e4d1d72984017d "HTTP/1.1 202 Accepted"
2025-05-12 18:43:24,477 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 18:43:24,536 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=ece79fe0e8034a75a9163b01ff62f71e "HTTP/1.1 202 Accepted"
2025-05-12 18:43:24,588 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=511beb07bc8e404399e4d1d72984017d "HTTP/1.1 202 Accepted"
2025-05-12 18:43:24,642 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=511beb07bc8e404399e4d1d72984017d "HTTP/1.1 202 Accepted"
2025-05-12 18:43:24,695 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=511beb07bc8e404399e4d1d72984017d "HTTP/1.1 202 Accepted"
2025-05-12 18:43:24,696 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=ece79fe0e8034a75a9163b01ff62f71e "HTTP/1.1 202 Accepted"
2025-05-12 18:43:25,077 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 18:46:32,835 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:46:33,849 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:46:33,851 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:46:33,858 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:46:33,881 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:46:48,685 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:46:49,850 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:46:49,853 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:46:49,861 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:46:49,887 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:48:13,144 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:48:14,224 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:48:14,226 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:48:14,231 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:48:14,257 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:48:36,565 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 18:48:36,661 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 18:48:36,687 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 18:48:37,324 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:48:37,325 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=74d2f38860e3480c8b7a643fc40e3893
2025-05-12 18:48:37,325 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=74d2f38860e3480c8b7a643fc40e3893
2025-05-12 18:48:37,332 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=74d2f38860e3480c8b7a643fc40e3893 "HTTP/1.1 202 Accepted"
2025-05-12 18:48:37,334 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 18:48:37,965 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:48:37,966 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=07427df2a1f04006aeb0a5042c06bb85
2025-05-12 18:48:37,967 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=07427df2a1f04006aeb0a5042c06bb85
2025-05-12 18:48:37,974 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=07427df2a1f04006aeb0a5042c06bb85 "HTTP/1.1 202 Accepted"
2025-05-12 18:48:37,992 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=74d2f38860e3480c8b7a643fc40e3893 "HTTP/1.1 202 Accepted"
2025-05-12 18:48:38,024 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=07427df2a1f04006aeb0a5042c06bb85 "HTTP/1.1 202 Accepted"
2025-05-12 18:48:38,045 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=74d2f38860e3480c8b7a643fc40e3893 "HTTP/1.1 202 Accepted"
2025-05-12 18:48:38,089 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=07427df2a1f04006aeb0a5042c06bb85 "HTTP/1.1 202 Accepted"
2025-05-12 18:48:38,642 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 18:48:38,702 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=74d2f38860e3480c8b7a643fc40e3893 "HTTP/1.1 202 Accepted"
2025-05-12 18:48:38,765 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=07427df2a1f04006aeb0a5042c06bb85 "HTTP/1.1 202 Accepted"
2025-05-12 18:48:38,818 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=07427df2a1f04006aeb0a5042c06bb85 "HTTP/1.1 202 Accepted"
2025-05-12 18:48:38,872 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=74d2f38860e3480c8b7a643fc40e3893 "HTTP/1.1 202 Accepted"
2025-05-12 18:48:38,882 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=07427df2a1f04006aeb0a5042c06bb85 "HTTP/1.1 202 Accepted"
2025-05-12 18:48:39,276 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 18:48:58,411 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 18:48:58,521 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 18:48:58,538 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 18:48:59,595 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:48:59,596 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=42688789eb45444faedae64623937af8
2025-05-12 18:48:59,597 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=42688789eb45444faedae64623937af8
2025-05-12 18:48:59,608 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=42688789eb45444faedae64623937af8 "HTTP/1.1 202 Accepted"
2025-05-12 18:48:59,611 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 18:49:00,348 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:49:00,349 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=49c2c0641b9e4b9ebaaf56a6e60a884d
2025-05-12 18:49:00,349 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=49c2c0641b9e4b9ebaaf56a6e60a884d
2025-05-12 18:49:00,355 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=49c2c0641b9e4b9ebaaf56a6e60a884d "HTTP/1.1 202 Accepted"
2025-05-12 18:49:00,374 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=42688789eb45444faedae64623937af8 "HTTP/1.1 202 Accepted"
2025-05-12 18:49:00,405 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=49c2c0641b9e4b9ebaaf56a6e60a884d "HTTP/1.1 202 Accepted"
2025-05-12 18:49:00,426 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=42688789eb45444faedae64623937af8 "HTTP/1.1 202 Accepted"
2025-05-12 18:49:00,459 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=49c2c0641b9e4b9ebaaf56a6e60a884d "HTTP/1.1 202 Accepted"
2025-05-12 18:49:01,020 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 18:49:59,619 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 18:49:59,702 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 18:49:59,714 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 18:50:00,341 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:50:00,342 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=1fa732927a224e61bbcc8a69682f1b00
2025-05-12 18:50:00,342 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=1fa732927a224e61bbcc8a69682f1b00
2025-05-12 18:50:00,351 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=1fa732927a224e61bbcc8a69682f1b00 "HTTP/1.1 202 Accepted"
2025-05-12 18:50:00,353 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 18:50:00,982 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:50:00,983 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=f53589d9fa824e28a4837d5c9731d4ed
2025-05-12 18:50:00,984 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=f53589d9fa824e28a4837d5c9731d4ed
2025-05-12 18:50:00,992 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f53589d9fa824e28a4837d5c9731d4ed "HTTP/1.1 202 Accepted"
2025-05-12 18:50:00,999 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=1fa732927a224e61bbcc8a69682f1b00 "HTTP/1.1 202 Accepted"
2025-05-12 18:50:01,051 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f53589d9fa824e28a4837d5c9731d4ed "HTTP/1.1 202 Accepted"
2025-05-12 18:50:01,053 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=1fa732927a224e61bbcc8a69682f1b00 "HTTP/1.1 202 Accepted"
2025-05-12 18:50:01,104 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f53589d9fa824e28a4837d5c9731d4ed "HTTP/1.1 202 Accepted"
2025-05-12 18:50:02,127 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 18:50:02,193 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=1fa732927a224e61bbcc8a69682f1b00 "HTTP/1.1 202 Accepted"
2025-05-12 18:50:02,246 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f53589d9fa824e28a4837d5c9731d4ed "HTTP/1.1 202 Accepted"
2025-05-12 18:50:02,300 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f53589d9fa824e28a4837d5c9731d4ed "HTTP/1.1 202 Accepted"
2025-05-12 18:50:03,061 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f53589d9fa824e28a4837d5c9731d4ed "HTTP/1.1 202 Accepted"
2025-05-12 18:50:03,072 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=1fa732927a224e61bbcc8a69682f1b00 "HTTP/1.1 202 Accepted"
2025-05-12 18:50:04,093 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 18:53:43,209 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:53:44,488 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:53:44,490 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:53:44,496 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:53:44,518 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:54:19,681 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-12 18:54:20,790 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-12 18:54:20,791 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-12 18:54:20,797 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:54:20,818 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:55:18,682 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 18:55:19,697 WARNING Overriding of current TracerProvider is not allowed
2025-05-12 18:55:19,907 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-12 18:55:21,497 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-12 18:55:21,499 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=e0b958bc7eb24ae2a04df834e1759520
2025-05-12 18:55:21,499 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=e0b958bc7eb24ae2a04df834e1759520
2025-05-12 18:55:21,506 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e0b958bc7eb24ae2a04df834e1759520 "HTTP/1.1 202 Accepted"
2025-05-12 18:55:21,510 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-12 18:55:22,236 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-12 18:55:22,237 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=876eff5185c54ce1b91a8764f89fadee
2025-05-12 18:55:22,237 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=876eff5185c54ce1b91a8764f89fadee
2025-05-12 18:55:22,244 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=876eff5185c54ce1b91a8764f89fadee "HTTP/1.1 202 Accepted"
2025-05-12 18:55:22,272 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e0b958bc7eb24ae2a04df834e1759520 "HTTP/1.1 202 Accepted"
2025-05-12 18:55:22,312 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=876eff5185c54ce1b91a8764f89fadee "HTTP/1.1 202 Accepted"
2025-05-12 18:55:22,323 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e0b958bc7eb24ae2a04df834e1759520 "HTTP/1.1 202 Accepted"
2025-05-12 18:55:22,366 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=876eff5185c54ce1b91a8764f89fadee "HTTP/1.1 202 Accepted"
2025-05-12 18:55:23,637 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-12 18:55:23,784 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e0b958bc7eb24ae2a04df834e1759520 "HTTP/1.1 202 Accepted"
2025-05-12 18:55:23,837 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=876eff5185c54ce1b91a8764f89fadee "HTTP/1.1 202 Accepted"
2025-05-12 18:55:23,889 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=876eff5185c54ce1b91a8764f89fadee "HTTP/1.1 202 Accepted"
2025-05-12 18:55:24,514 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=876eff5185c54ce1b91a8764f89fadee "HTTP/1.1 202 Accepted"
2025-05-12 18:55:24,515 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e0b958bc7eb24ae2a04df834e1759520 "HTTP/1.1 202 Accepted"
2025-05-12 18:55:25,429 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-19 14:37:23,190 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-19 14:37:24,307 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-19 14:37:24,308 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-19 14:37:24,319 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-19 14:37:24,382 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 10:35:13,052 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 10:35:55,624 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 10:35:56,294 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 10:35:56,295 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 10:35:56,549 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 10:35:56,625 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 11:15:54,003 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 11:15:54,029 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 11:15:54,654 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 11:15:54,656 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=e05c0ed87335425e8cd0561a17691513
2025-05-22 11:15:54,656 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=e05c0ed87335425e8cd0561a17691513
2025-05-22 11:15:54,675 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e05c0ed87335425e8cd0561a17691513 "HTTP/1.1 202 Accepted"
2025-05-22 11:15:54,699 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 11:15:55,342 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 11:15:55,343 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=928474799c11431096c927090f79c5bb
2025-05-22 11:15:55,343 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=928474799c11431096c927090f79c5bb
2025-05-22 11:15:55,357 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e05c0ed87335425e8cd0561a17691513 "HTTP/1.1 202 Accepted"
2025-05-22 11:15:55,400 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=928474799c11431096c927090f79c5bb "HTTP/1.1 202 Accepted"
2025-05-22 11:15:55,407 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=928474799c11431096c927090f79c5bb "HTTP/1.1 202 Accepted"
2025-05-22 11:15:55,452 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e05c0ed87335425e8cd0561a17691513 "HTTP/1.1 202 Accepted"
2025-05-22 11:15:55,462 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=928474799c11431096c927090f79c5bb "HTTP/1.1 202 Accepted"
2025-05-22 11:15:57,294 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 11:33:49,012 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 11:33:49,022 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 11:33:49,593 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 11:33:49,594 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=036dbd1a3f8c4079862ecde0031b76b5
2025-05-22 11:33:49,594 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=036dbd1a3f8c4079862ecde0031b76b5
2025-05-22 11:33:49,603 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=036dbd1a3f8c4079862ecde0031b76b5 "HTTP/1.1 202 Accepted"
2025-05-22 11:33:49,607 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 11:33:50,154 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 11:33:50,155 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=c87330d091de4d67b839482becdb5cfe
2025-05-22 11:33:50,155 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=c87330d091de4d67b839482becdb5cfe
2025-05-22 11:33:50,162 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c87330d091de4d67b839482becdb5cfe "HTTP/1.1 202 Accepted"
2025-05-22 11:33:50,172 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=036dbd1a3f8c4079862ecde0031b76b5 "HTTP/1.1 202 Accepted"
2025-05-22 11:33:50,214 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c87330d091de4d67b839482becdb5cfe "HTTP/1.1 202 Accepted"
2025-05-22 11:33:50,225 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=036dbd1a3f8c4079862ecde0031b76b5 "HTTP/1.1 202 Accepted"
2025-05-22 11:33:50,276 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c87330d091de4d67b839482becdb5cfe "HTTP/1.1 202 Accepted"
2025-05-22 11:33:51,614 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 11:34:06,259 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 11:34:06,273 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 11:34:06,806 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 11:34:06,807 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=03a01d7367394ceaa82bbf3febeca8a2
2025-05-22 11:34:06,808 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=03a01d7367394ceaa82bbf3febeca8a2
2025-05-22 11:34:06,813 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=03a01d7367394ceaa82bbf3febeca8a2 "HTTP/1.1 202 Accepted"
2025-05-22 11:34:06,815 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 11:34:07,380 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 11:34:07,381 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=c8f4e8dd53934812b641fd7e854fe721
2025-05-22 11:34:07,381 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=c8f4e8dd53934812b641fd7e854fe721
2025-05-22 11:34:07,389 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c8f4e8dd53934812b641fd7e854fe721 "HTTP/1.1 202 Accepted"
2025-05-22 11:34:07,401 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=03a01d7367394ceaa82bbf3febeca8a2 "HTTP/1.1 202 Accepted"
2025-05-22 11:34:07,441 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c8f4e8dd53934812b641fd7e854fe721 "HTTP/1.1 202 Accepted"
2025-05-22 11:34:07,452 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=03a01d7367394ceaa82bbf3febeca8a2 "HTTP/1.1 202 Accepted"
2025-05-22 11:34:07,504 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c8f4e8dd53934812b641fd7e854fe721 "HTTP/1.1 202 Accepted"
2025-05-22 11:34:07,695 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-22 11:34:07,696 INFO Retrying request to /openai/v1/chat/completions in 2.000000 seconds
2025-05-22 11:34:10,983 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 11:34:55,771 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 11:34:55,790 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 11:34:56,478 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 11:34:56,479 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=4b7d49f6443944539bc4f9c57d9617ec
2025-05-22 11:34:56,479 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=4b7d49f6443944539bc4f9c57d9617ec
2025-05-22 11:34:56,486 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=4b7d49f6443944539bc4f9c57d9617ec "HTTP/1.1 202 Accepted"
2025-05-22 11:34:56,488 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 11:34:57,164 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 11:34:57,165 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=e82a52eb8d48483aabe7ca1917bcdfee
2025-05-22 11:34:57,165 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=e82a52eb8d48483aabe7ca1917bcdfee
2025-05-22 11:34:57,174 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e82a52eb8d48483aabe7ca1917bcdfee "HTTP/1.1 202 Accepted"
2025-05-22 11:34:57,194 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=4b7d49f6443944539bc4f9c57d9617ec "HTTP/1.1 202 Accepted"
2025-05-22 11:34:57,225 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e82a52eb8d48483aabe7ca1917bcdfee "HTTP/1.1 202 Accepted"
2025-05-22 11:34:57,246 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=4b7d49f6443944539bc4f9c57d9617ec "HTTP/1.1 202 Accepted"
2025-05-22 11:34:57,288 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e82a52eb8d48483aabe7ca1917bcdfee "HTTP/1.1 202 Accepted"
2025-05-22 11:34:58,559 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 11:35:21,247 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 11:35:21,260 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 11:35:21,798 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 11:35:21,799 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=d16abc68fc1b4eaab7aec387b07ddaf2
2025-05-22 11:35:21,800 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=d16abc68fc1b4eaab7aec387b07ddaf2
2025-05-22 11:35:21,808 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d16abc68fc1b4eaab7aec387b07ddaf2 "HTTP/1.1 202 Accepted"
2025-05-22 11:35:21,810 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 11:35:22,398 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 11:35:22,399 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=f58f2eadeeb44b62a23ca05aa659c905
2025-05-22 11:35:22,400 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=f58f2eadeeb44b62a23ca05aa659c905
2025-05-22 11:35:22,407 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f58f2eadeeb44b62a23ca05aa659c905 "HTTP/1.1 202 Accepted"
2025-05-22 11:35:22,426 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d16abc68fc1b4eaab7aec387b07ddaf2 "HTTP/1.1 202 Accepted"
2025-05-22 11:35:22,457 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f58f2eadeeb44b62a23ca05aa659c905 "HTTP/1.1 202 Accepted"
2025-05-22 11:35:22,488 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d16abc68fc1b4eaab7aec387b07ddaf2 "HTTP/1.1 202 Accepted"
2025-05-22 11:35:22,509 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f58f2eadeeb44b62a23ca05aa659c905 "HTTP/1.1 202 Accepted"
2025-05-22 11:35:23,743 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 11:37:34,274 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 11:37:34,292 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 11:37:35,287 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 11:37:35,289 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=c9e0cca4bed94f84a876f7dd6ed5c478
2025-05-22 11:37:35,289 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=c9e0cca4bed94f84a876f7dd6ed5c478
2025-05-22 11:37:35,330 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=c9e0cca4bed94f84a876f7dd6ed5c478 "HTTP/1.1 202 Accepted"
2025-05-22 11:37:35,332 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 11:37:35,911 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 11:37:35,912 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=336f4d6b583b49a48c8dc535a6de711d
2025-05-22 11:37:35,912 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=336f4d6b583b49a48c8dc535a6de711d
2025-05-22 11:37:35,919 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=336f4d6b583b49a48c8dc535a6de711d "HTTP/1.1 202 Accepted"
2025-05-22 11:37:35,930 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=c9e0cca4bed94f84a876f7dd6ed5c478 "HTTP/1.1 202 Accepted"
2025-05-22 11:37:35,972 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=336f4d6b583b49a48c8dc535a6de711d "HTTP/1.1 202 Accepted"
2025-05-22 11:37:35,983 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=c9e0cca4bed94f84a876f7dd6ed5c478 "HTTP/1.1 202 Accepted"
2025-05-22 11:37:36,024 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=336f4d6b583b49a48c8dc535a6de711d "HTTP/1.1 202 Accepted"
2025-05-22 11:37:36,721 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 11:39:43,276 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 11:39:43,287 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 11:39:43,811 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 11:39:43,812 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=fb36ceee270545f197aed2251d3d65e3
2025-05-22 11:39:43,812 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=fb36ceee270545f197aed2251d3d65e3
2025-05-22 11:39:43,820 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=fb36ceee270545f197aed2251d3d65e3 "HTTP/1.1 202 Accepted"
2025-05-22 11:39:43,821 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 11:39:44,380 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 11:39:44,381 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=61b6e58c78594a2f9a6ae594c341549f
2025-05-22 11:39:44,381 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=61b6e58c78594a2f9a6ae594c341549f
2025-05-22 11:39:44,387 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=61b6e58c78594a2f9a6ae594c341549f "HTTP/1.1 202 Accepted"
2025-05-22 11:39:44,402 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=fb36ceee270545f197aed2251d3d65e3 "HTTP/1.1 202 Accepted"
2025-05-22 11:39:44,433 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=61b6e58c78594a2f9a6ae594c341549f "HTTP/1.1 202 Accepted"
2025-05-22 11:39:44,454 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=fb36ceee270545f197aed2251d3d65e3 "HTTP/1.1 202 Accepted"
2025-05-22 11:39:44,486 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=61b6e58c78594a2f9a6ae594c341549f "HTTP/1.1 202 Accepted"
2025-05-22 11:39:45,850 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 11:46:48,012 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 11:46:54,587 INFO Creating new collection: mem0_memories768
2025-05-22 11:46:55,214 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-22 11:46:57,427 INFO Creating new collection: mem0migrations
2025-05-22 11:46:58,033 INFO Successfully created collection mem0migrations with dimension 768
2025-05-22 11:46:58,218 INFO Inserting 1 vectors into collection mem0migrations
2025-05-22 11:46:58,474 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-22 11:46:58,474 INFO Retrieving memories for user_id=user-966tg1ki, query='Who are you :'
2025-05-22 11:47:01,288 INFO Retrieved memory for user_id=user-966tg1ki, query='Who are you :': {'results': []}
2025-05-22 11:47:01,354 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 11:47:01,365 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 11:47:01,925 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 11:47:01,926 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=8e823de080d2453aa55e4aae55ec646f
2025-05-22 11:47:01,927 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=8e823de080d2453aa55e4aae55ec646f
2025-05-22 11:47:01,948 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=8e823de080d2453aa55e4aae55ec646f "HTTP/1.1 202 Accepted"
2025-05-22 11:47:01,950 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 11:47:02,579 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 11:47:02,580 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=6f1e7cb1c524498f855b5e2a72cd3c85
2025-05-22 11:47:02,581 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=6f1e7cb1c524498f855b5e2a72cd3c85
2025-05-22 11:47:02,588 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6f1e7cb1c524498f855b5e2a72cd3c85 "HTTP/1.1 202 Accepted"
2025-05-22 11:47:02,610 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=8e823de080d2453aa55e4aae55ec646f "HTTP/1.1 202 Accepted"
2025-05-22 11:47:02,651 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6f1e7cb1c524498f855b5e2a72cd3c85 "HTTP/1.1 202 Accepted"
2025-05-22 11:47:02,672 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=8e823de080d2453aa55e4aae55ec646f "HTTP/1.1 202 Accepted"
2025-05-22 11:47:02,704 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6f1e7cb1c524498f855b5e2a72cd3c85 "HTTP/1.1 202 Accepted"
2025-05-22 11:47:03,927 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 11:48:03,714 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 11:48:05,440 INFO Creating new collection: mem0_memories768
2025-05-22 11:48:06,148 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-22 11:48:08,322 INFO Creating new collection: mem0migrations
2025-05-22 11:48:08,977 INFO Successfully created collection mem0migrations with dimension 768
2025-05-22 11:48:09,214 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-22 11:48:09,215 INFO Retrieving memories for user_id=user-flk4f31x, query='who are you ?'
2025-05-22 11:48:11,766 INFO Retrieved memory for user_id=user-flk4f31x, query='who are you ?': {'results': []}
2025-05-22 11:48:11,850 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 11:48:11,862 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 11:48:12,439 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 11:48:12,440 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=443bc7d35db349738b4d173a726c9d41
2025-05-22 11:48:12,440 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=443bc7d35db349738b4d173a726c9d41
2025-05-22 11:48:12,447 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=443bc7d35db349738b4d173a726c9d41 "HTTP/1.1 202 Accepted"
2025-05-22 11:48:12,449 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 11:48:13,219 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 11:48:13,220 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=7aa8e5d41c804787806cc1c1b8841b0e
2025-05-22 11:48:13,220 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=7aa8e5d41c804787806cc1c1b8841b0e
2025-05-22 11:48:13,222 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=443bc7d35db349738b4d173a726c9d41 "HTTP/1.1 202 Accepted"
2025-05-22 11:48:13,228 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=7aa8e5d41c804787806cc1c1b8841b0e "HTTP/1.1 202 Accepted"
2025-05-22 11:48:13,285 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=443bc7d35db349738b4d173a726c9d41 "HTTP/1.1 202 Accepted"
2025-05-22 11:48:13,296 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=7aa8e5d41c804787806cc1c1b8841b0e "HTTP/1.1 202 Accepted"
2025-05-22 11:48:13,348 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=7aa8e5d41c804787806cc1c1b8841b0e "HTTP/1.1 202 Accepted"
2025-05-22 11:48:14,360 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 11:48:16,115 INFO Creating new collection: mem0_memories768
2025-05-22 11:48:16,742 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-22 11:48:18,986 INFO Creating new collection: mem0migrations
2025-05-22 11:48:19,640 INFO Successfully created collection mem0migrations with dimension 768
2025-05-22 11:48:19,895 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-22 11:48:19,895 INFO Adding memory for user_id=user-flk4f31x
2025-05-22 11:48:22,383 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 11:48:22,483 INFO Total existing memories: 0
2025-05-22 11:48:23,148 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 11:57:55,605 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 11:57:55,618 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 11:57:56,175 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 11:57:56,176 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=bb973e4cb982422881e2a3584305f751
2025-05-22 11:57:56,176 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=bb973e4cb982422881e2a3584305f751
2025-05-22 11:57:56,182 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=bb973e4cb982422881e2a3584305f751 "HTTP/1.1 202 Accepted"
2025-05-22 11:57:56,184 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 11:57:56,751 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 11:57:56,752 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=d165d6d355fc439aa5cc93966405fa8d
2025-05-22 11:57:56,752 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=d165d6d355fc439aa5cc93966405fa8d
2025-05-22 11:57:56,759 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d165d6d355fc439aa5cc93966405fa8d "HTTP/1.1 202 Accepted"
2025-05-22 11:57:56,760 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=bb973e4cb982422881e2a3584305f751 "HTTP/1.1 202 Accepted"
2025-05-22 11:57:56,813 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d165d6d355fc439aa5cc93966405fa8d "HTTP/1.1 202 Accepted"
2025-05-22 11:57:56,814 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=bb973e4cb982422881e2a3584305f751 "HTTP/1.1 202 Accepted"
2025-05-22 11:57:56,865 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d165d6d355fc439aa5cc93966405fa8d "HTTP/1.1 202 Accepted"
2025-05-22 11:57:58,354 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 11:58:15,763 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 11:58:15,778 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 11:58:16,390 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 11:58:16,391 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=9b4343ee4fbb409db9d33669068ecc7d
2025-05-22 11:58:16,391 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=9b4343ee4fbb409db9d33669068ecc7d
2025-05-22 11:58:16,397 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9b4343ee4fbb409db9d33669068ecc7d "HTTP/1.1 202 Accepted"
2025-05-22 11:58:16,398 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 11:58:16,959 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 11:58:16,960 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=5d2fe35d1dfc43299e671293c6bb9f8f
2025-05-22 11:58:16,960 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=5d2fe35d1dfc43299e671293c6bb9f8f
2025-05-22 11:58:16,967 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5d2fe35d1dfc43299e671293c6bb9f8f "HTTP/1.1 202 Accepted"
2025-05-22 11:58:16,985 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9b4343ee4fbb409db9d33669068ecc7d "HTTP/1.1 202 Accepted"
2025-05-22 11:58:17,027 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5d2fe35d1dfc43299e671293c6bb9f8f "HTTP/1.1 202 Accepted"
2025-05-22 11:58:17,037 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9b4343ee4fbb409db9d33669068ecc7d "HTTP/1.1 202 Accepted"
2025-05-22 11:58:17,079 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5d2fe35d1dfc43299e671293c6bb9f8f "HTTP/1.1 202 Accepted"
2025-05-22 11:58:18,408 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 11:59:37,823 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 11:59:39,513 INFO Creating new collection: mem0_memories768
2025-05-22 11:59:40,167 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-22 11:59:42,196 INFO Creating new collection: mem0migrations
2025-05-22 11:59:42,862 INFO Successfully created collection mem0migrations with dimension 768
2025-05-22 11:59:43,108 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-22 11:59:43,108 INFO Retrieving memories for user_id=user-flk4f31x, query='Who are you ?'
2025-05-22 11:59:45,637 INFO Retrieved memory for user_id=user-flk4f31x, query='Who are you ?': {'results': []}
2025-05-22 11:59:45,704 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 11:59:45,713 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 11:59:46,245 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 11:59:46,246 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=66d363042b9b4ea39a3251a8ac1a452d
2025-05-22 11:59:46,246 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=66d363042b9b4ea39a3251a8ac1a452d
2025-05-22 11:59:46,255 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=66d363042b9b4ea39a3251a8ac1a452d "HTTP/1.1 202 Accepted"
2025-05-22 11:59:46,257 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 11:59:46,836 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=66d363042b9b4ea39a3251a8ac1a452d "HTTP/1.1 202 Accepted"
2025-05-22 11:59:46,838 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 11:59:46,839 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=d92f8b89bcc34e0f870e4219d401433e
2025-05-22 11:59:46,840 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=d92f8b89bcc34e0f870e4219d401433e
2025-05-22 11:59:46,865 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d92f8b89bcc34e0f870e4219d401433e "HTTP/1.1 202 Accepted"
2025-05-22 11:59:46,919 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d92f8b89bcc34e0f870e4219d401433e "HTTP/1.1 202 Accepted"
2025-05-22 11:59:46,930 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=66d363042b9b4ea39a3251a8ac1a452d "HTTP/1.1 202 Accepted"
2025-05-22 11:59:46,982 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d92f8b89bcc34e0f870e4219d401433e "HTTP/1.1 202 Accepted"
2025-05-22 11:59:47,748 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 11:59:49,239 INFO Creating new collection: mem0_memories768
2025-05-22 11:59:49,836 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-22 11:59:51,856 INFO Creating new collection: mem0migrations
2025-05-22 11:59:52,477 INFO Successfully created collection mem0migrations with dimension 768
2025-05-22 11:59:52,713 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-22 11:59:52,713 INFO Adding memory for user_id=user-flk4f31x
2025-05-22 11:59:53,955 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 11:59:54,156 INFO Total existing memories: 0
2025-05-22 11:59:54,953 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 12:16:18,473 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 12:16:18,486 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 12:16:19,561 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 12:16:19,563 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=7c3af423a2c7456e8ee3f503b04e39df
2025-05-22 12:16:19,564 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=7c3af423a2c7456e8ee3f503b04e39df
2025-05-22 12:16:19,573 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=7c3af423a2c7456e8ee3f503b04e39df "HTTP/1.1 202 Accepted"
2025-05-22 12:16:19,577 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 12:16:20,824 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=7c3af423a2c7456e8ee3f503b04e39df "HTTP/1.1 202 Accepted"
2025-05-22 12:16:20,837 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 12:16:20,838 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=5dcd51a9943541f8b86fa468f1ee5c67
2025-05-22 12:16:20,838 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=5dcd51a9943541f8b86fa468f1ee5c67
2025-05-22 12:16:21,488 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5dcd51a9943541f8b86fa468f1ee5c67 "HTTP/1.1 202 Accepted"
2025-05-22 12:16:21,544 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5dcd51a9943541f8b86fa468f1ee5c67 "HTTP/1.1 202 Accepted"
2025-05-22 12:16:21,555 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=7c3af423a2c7456e8ee3f503b04e39df "HTTP/1.1 202 Accepted"
2025-05-22 12:16:21,596 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5dcd51a9943541f8b86fa468f1ee5c67 "HTTP/1.1 202 Accepted"
2025-05-22 12:16:23,117 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 12:19:42,558 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 12:19:42,653 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 12:19:42,661 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 12:19:43,244 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 12:19:43,245 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=1d02d6a58146436ba81d8eba8b7231c7
2025-05-22 12:19:43,245 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=1d02d6a58146436ba81d8eba8b7231c7
2025-05-22 12:19:43,252 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=1d02d6a58146436ba81d8eba8b7231c7 "HTTP/1.1 202 Accepted"
2025-05-22 12:19:43,253 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 12:19:43,817 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 12:19:43,818 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=f288d2d565224b6eb9aa628286291f74
2025-05-22 12:19:43,819 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=f288d2d565224b6eb9aa628286291f74
2025-05-22 12:19:43,826 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f288d2d565224b6eb9aa628286291f74 "HTTP/1.1 202 Accepted"
2025-05-22 12:19:43,849 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=1d02d6a58146436ba81d8eba8b7231c7 "HTTP/1.1 202 Accepted"
2025-05-22 12:19:43,880 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f288d2d565224b6eb9aa628286291f74 "HTTP/1.1 202 Accepted"
2025-05-22 12:19:43,912 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=1d02d6a58146436ba81d8eba8b7231c7 "HTTP/1.1 202 Accepted"
2025-05-22 12:19:43,933 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f288d2d565224b6eb9aa628286291f74 "HTTP/1.1 202 Accepted"
2025-05-22 12:19:44,164 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-22 12:22:21,672 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 12:22:21,755 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 12:22:21,764 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 12:22:22,319 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 12:22:22,320 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=7c8e0a82bf07417aa5718bc786f994c2
2025-05-22 12:22:22,320 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=7c8e0a82bf07417aa5718bc786f994c2
2025-05-22 12:22:22,328 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=7c8e0a82bf07417aa5718bc786f994c2 "HTTP/1.1 202 Accepted"
2025-05-22 12:22:22,329 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 12:22:22,909 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 12:22:22,910 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=b6010b7f48f248b98d3a542c27354692
2025-05-22 12:22:22,910 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=b6010b7f48f248b98d3a542c27354692
2025-05-22 12:22:22,938 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=7c8e0a82bf07417aa5718bc786f994c2 "HTTP/1.1 202 Accepted"
2025-05-22 12:22:22,942 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=b6010b7f48f248b98d3a542c27354692 "HTTP/1.1 202 Accepted"
2025-05-22 12:22:22,990 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=b6010b7f48f248b98d3a542c27354692 "HTTP/1.1 202 Accepted"
2025-05-22 12:22:23,001 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=7c8e0a82bf07417aa5718bc786f994c2 "HTTP/1.1 202 Accepted"
2025-05-22 12:22:23,042 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=b6010b7f48f248b98d3a542c27354692 "HTTP/1.1 202 Accepted"
2025-05-22 12:22:23,233 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-22 12:24:53,712 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 12:24:53,783 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 12:24:53,790 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 12:24:54,317 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 12:24:54,317 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=81433817360344c690407e18d7daeaaa
2025-05-22 12:24:54,318 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=81433817360344c690407e18d7daeaaa
2025-05-22 12:24:54,323 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=81433817360344c690407e18d7daeaaa "HTTP/1.1 202 Accepted"
2025-05-22 12:24:54,324 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 12:24:54,883 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 12:24:54,884 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=a461506dec5a49d1a16aaf426ce7f6b0
2025-05-22 12:24:54,885 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=a461506dec5a49d1a16aaf426ce7f6b0
2025-05-22 12:24:54,891 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=a461506dec5a49d1a16aaf426ce7f6b0 "HTTP/1.1 202 Accepted"
2025-05-22 12:24:54,915 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=81433817360344c690407e18d7daeaaa "HTTP/1.1 202 Accepted"
2025-05-22 12:24:54,946 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=a461506dec5a49d1a16aaf426ce7f6b0 "HTTP/1.1 202 Accepted"
2025-05-22 12:24:54,967 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=81433817360344c690407e18d7daeaaa "HTTP/1.1 202 Accepted"
2025-05-22 12:24:55,009 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=a461506dec5a49d1a16aaf426ce7f6b0 "HTTP/1.1 202 Accepted"
2025-05-22 12:24:55,277 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-22 13:10:37,626 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 13:10:37,710 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 13:10:37,721 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 13:10:38,269 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 13:10:38,270 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=30c324eda0b14e05ac5e80ddc36c7056
2025-05-22 13:10:38,270 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=30c324eda0b14e05ac5e80ddc36c7056
2025-05-22 13:10:38,276 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=30c324eda0b14e05ac5e80ddc36c7056 "HTTP/1.1 202 Accepted"
2025-05-22 13:10:38,278 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 13:10:38,860 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 13:10:38,861 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=e56a0844e6114f3e988bda3f3c69708b
2025-05-22 13:10:38,861 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=e56a0844e6114f3e988bda3f3c69708b
2025-05-22 13:10:38,868 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e56a0844e6114f3e988bda3f3c69708b "HTTP/1.1 202 Accepted"
2025-05-22 13:10:38,889 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=30c324eda0b14e05ac5e80ddc36c7056 "HTTP/1.1 202 Accepted"
2025-05-22 13:10:38,931 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e56a0844e6114f3e988bda3f3c69708b "HTTP/1.1 202 Accepted"
2025-05-22 13:10:38,942 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=30c324eda0b14e05ac5e80ddc36c7056 "HTTP/1.1 202 Accepted"
2025-05-22 13:10:38,984 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e56a0844e6114f3e988bda3f3c69708b "HTTP/1.1 202 Accepted"
2025-05-22 13:10:39,257 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-22 13:36:28,440 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 13:36:28,519 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 13:36:28,530 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 13:36:29,045 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 13:36:29,046 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=7d893cddb209410fa7399eb986460db7
2025-05-22 13:36:29,046 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=7d893cddb209410fa7399eb986460db7
2025-05-22 13:36:29,087 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=7d893cddb209410fa7399eb986460db7 "HTTP/1.1 202 Accepted"
2025-05-22 13:36:29,089 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 13:36:29,600 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=7d893cddb209410fa7399eb986460db7 "HTTP/1.1 202 Accepted"
2025-05-22 13:36:29,634 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 13:36:29,635 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=c5463223c468416985d3e18f215769f2
2025-05-22 13:36:29,635 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=c5463223c468416985d3e18f215769f2
2025-05-22 13:36:29,642 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c5463223c468416985d3e18f215769f2 "HTTP/1.1 202 Accepted"
2025-05-22 13:36:29,696 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=7d893cddb209410fa7399eb986460db7 "HTTP/1.1 202 Accepted"
2025-05-22 13:36:29,707 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c5463223c468416985d3e18f215769f2 "HTTP/1.1 202 Accepted"
2025-05-22 13:36:29,762 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c5463223c468416985d3e18f215769f2 "HTTP/1.1 202 Accepted"
2025-05-22 13:36:30,088 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-22 13:37:25,729 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 13:37:25,862 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 13:37:25,893 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 13:37:26,652 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 13:37:26,652 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=0821853d08964139b8c0392db0c418d3
2025-05-22 13:37:26,653 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=0821853d08964139b8c0392db0c418d3
2025-05-22 13:37:26,670 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=0821853d08964139b8c0392db0c418d3 "HTTP/1.1 202 Accepted"
2025-05-22 13:37:26,671 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 13:37:27,249 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 13:37:27,250 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=c98e05b1f3d24209882c5aa46eb35567
2025-05-22 13:37:27,250 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=c98e05b1f3d24209882c5aa46eb35567
2025-05-22 13:37:27,256 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c98e05b1f3d24209882c5aa46eb35567 "HTTP/1.1 202 Accepted"
2025-05-22 13:37:27,269 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=0821853d08964139b8c0392db0c418d3 "HTTP/1.1 202 Accepted"
2025-05-22 13:37:27,322 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=0821853d08964139b8c0392db0c418d3 "HTTP/1.1 202 Accepted"
2025-05-22 13:37:27,323 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c98e05b1f3d24209882c5aa46eb35567 "HTTP/1.1 202 Accepted"
2025-05-22 13:37:27,375 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c98e05b1f3d24209882c5aa46eb35567 "HTTP/1.1 202 Accepted"
2025-05-22 13:37:27,561 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-22 13:41:03,328 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 13:41:04,520 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 13:41:04,521 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 13:41:04,525 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 13:41:04,545 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 13:41:30,034 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 13:41:30,127 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 13:41:30,135 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 13:41:30,672 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 13:41:30,673 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=d7d5279b747941b19585bc83aeb7d2b1
2025-05-22 13:41:30,673 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=d7d5279b747941b19585bc83aeb7d2b1
2025-05-22 13:41:30,680 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d7d5279b747941b19585bc83aeb7d2b1 "HTTP/1.1 202 Accepted"
2025-05-22 13:41:30,681 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 13:41:31,218 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 13:41:31,218 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=10e241892dbe4e0197a52906e17b2f98
2025-05-22 13:41:31,219 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=10e241892dbe4e0197a52906e17b2f98
2025-05-22 13:41:31,225 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=10e241892dbe4e0197a52906e17b2f98 "HTTP/1.1 202 Accepted"
2025-05-22 13:41:31,235 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d7d5279b747941b19585bc83aeb7d2b1 "HTTP/1.1 202 Accepted"
2025-05-22 13:41:31,277 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=10e241892dbe4e0197a52906e17b2f98 "HTTP/1.1 202 Accepted"
2025-05-22 13:41:31,288 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d7d5279b747941b19585bc83aeb7d2b1 "HTTP/1.1 202 Accepted"
2025-05-22 13:41:31,330 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=10e241892dbe4e0197a52906e17b2f98 "HTTP/1.1 202 Accepted"
2025-05-22 13:41:31,579 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-22 13:42:48,263 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 13:42:51,753 INFO Creating new collection: mem0_memories768
2025-05-22 13:42:52,421 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-22 13:42:54,421 INFO Creating new collection: mem0migrations
2025-05-22 13:42:55,048 INFO Successfully created collection mem0migrations with dimension 768
2025-05-22 13:42:55,256 INFO Inserting 1 vectors into collection mem0migrations
2025-05-22 13:42:55,581 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-22 13:42:55,581 INFO Retrieving memories for user_id=user-51cj9vyj, query='hi'
2025-05-22 13:42:58,298 INFO Retrieved memory for user_id=user-51cj9vyj, query='hi': {'results': []}
2025-05-22 13:42:58,363 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 13:42:58,371 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 13:42:58,925 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 13:42:58,926 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=864e7ef3b0674210b87dff7bfcf8d3e4
2025-05-22 13:42:58,927 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=864e7ef3b0674210b87dff7bfcf8d3e4
2025-05-22 13:42:58,950 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=864e7ef3b0674210b87dff7bfcf8d3e4 "HTTP/1.1 202 Accepted"
2025-05-22 13:42:58,952 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 13:42:59,594 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 13:42:59,594 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=c918c4d11ec24a519dbbc5747015a648
2025-05-22 13:42:59,595 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=c918c4d11ec24a519dbbc5747015a648
2025-05-22 13:42:59,602 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c918c4d11ec24a519dbbc5747015a648 "HTTP/1.1 202 Accepted"
2025-05-22 13:42:59,626 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=864e7ef3b0674210b87dff7bfcf8d3e4 "HTTP/1.1 202 Accepted"
2025-05-22 13:42:59,657 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c918c4d11ec24a519dbbc5747015a648 "HTTP/1.1 202 Accepted"
2025-05-22 13:42:59,679 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=864e7ef3b0674210b87dff7bfcf8d3e4 "HTTP/1.1 202 Accepted"
2025-05-22 13:42:59,711 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c918c4d11ec24a519dbbc5747015a648 "HTTP/1.1 202 Accepted"
2025-05-22 13:42:59,878 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-22 13:48:15,353 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 13:48:16,461 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 13:48:16,462 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 13:48:16,466 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 13:48:16,492 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 13:48:27,598 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 13:48:31,114 INFO Creating new collection: mem0_memories768
2025-05-22 13:48:31,765 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-22 13:48:33,817 INFO Creating new collection: mem0migrations
2025-05-22 13:48:34,424 INFO Successfully created collection mem0migrations with dimension 768
2025-05-22 13:48:34,614 INFO Inserting 1 vectors into collection mem0migrations
2025-05-22 13:48:34,869 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-22 13:48:34,870 INFO Retrieving memories for user_id=user-nal1csox, query='ji'
2025-05-22 13:48:36,487 INFO Retrieved memory for user_id=user-nal1csox, query='ji': {'results': []}
2025-05-22 13:48:36,673 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 13:48:36,721 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 13:48:38,284 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 13:48:38,285 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=ce32c2af33714f10830aa93a97d8ead5
2025-05-22 13:48:38,286 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=ce32c2af33714f10830aa93a97d8ead5
2025-05-22 13:48:38,299 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=ce32c2af33714f10830aa93a97d8ead5 "HTTP/1.1 202 Accepted"
2025-05-22 13:48:38,303 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 13:48:40,843 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=ce32c2af33714f10830aa93a97d8ead5 "HTTP/1.1 202 Accepted"
2025-05-22 13:48:41,011 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 13:48:41,013 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=0eb5488e4261468fbbda7ca98edbe24b
2025-05-22 13:48:41,014 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=0eb5488e4261468fbbda7ca98edbe24b
2025-05-22 13:48:41,132 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=0eb5488e4261468fbbda7ca98edbe24b "HTTP/1.1 202 Accepted"
2025-05-22 13:48:41,201 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=0eb5488e4261468fbbda7ca98edbe24b "HTTP/1.1 202 Accepted"
2025-05-22 13:48:41,202 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=ce32c2af33714f10830aa93a97d8ead5 "HTTP/1.1 202 Accepted"
2025-05-22 13:48:41,306 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=0eb5488e4261468fbbda7ca98edbe24b "HTTP/1.1 202 Accepted"
2025-05-22 13:48:42,192 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-22 13:51:44,828 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 13:51:45,983 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 13:51:45,984 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 13:51:45,988 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 13:51:46,015 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 13:54:22,040 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 13:54:25,163 INFO Creating new collection: mem0_memories768
2025-05-22 13:54:25,809 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-22 13:54:28,165 INFO Creating new collection: mem0migrations
2025-05-22 13:54:28,783 INFO Successfully created collection mem0migrations with dimension 768
2025-05-22 13:54:28,966 INFO Inserting 1 vectors into collection mem0migrations
2025-05-22 13:54:29,207 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-22 13:54:29,208 INFO Retrieving memories for user_id=user-xjdyiphk, query='hi'
2025-05-22 13:54:30,728 INFO Retrieved memory for user_id=user-xjdyiphk, query='hi': {'results': []}
2025-05-22 13:54:30,790 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 13:54:30,798 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 13:54:31,317 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 13:54:31,317 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=dae6e62663cd40f5a6eacab6e19510e3
2025-05-22 13:54:31,318 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=dae6e62663cd40f5a6eacab6e19510e3
2025-05-22 13:54:31,324 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=dae6e62663cd40f5a6eacab6e19510e3 "HTTP/1.1 202 Accepted"
2025-05-22 13:54:31,325 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 13:54:31,849 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 13:54:31,849 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=549823d6bb8842db9ab09653bb4de0f1
2025-05-22 13:54:31,850 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=549823d6bb8842db9ab09653bb4de0f1
2025-05-22 13:54:31,857 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=549823d6bb8842db9ab09653bb4de0f1 "HTTP/1.1 202 Accepted"
2025-05-22 13:54:31,872 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=dae6e62663cd40f5a6eacab6e19510e3 "HTTP/1.1 202 Accepted"
2025-05-22 13:54:31,925 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=549823d6bb8842db9ab09653bb4de0f1 "HTTP/1.1 202 Accepted"
2025-05-22 13:54:31,935 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=dae6e62663cd40f5a6eacab6e19510e3 "HTTP/1.1 202 Accepted"
2025-05-22 13:54:31,988 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=549823d6bb8842db9ab09653bb4de0f1 "HTTP/1.1 202 Accepted"
2025-05-22 13:54:32,314 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-22 13:57:11,626 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 13:57:12,781 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 13:57:12,782 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 13:57:12,788 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 13:57:12,808 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 13:57:29,247 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 13:57:30,883 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 13:57:30,892 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 13:57:30,894 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 13:57:30,937 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:00:35,880 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 14:00:36,953 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 14:00:36,954 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 14:00:36,959 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:00:36,980 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:00:58,920 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:01:02,146 INFO Creating new collection: mem0_memories768
2025-05-22 14:01:02,782 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-22 14:01:04,828 INFO Creating new collection: mem0migrations
2025-05-22 14:01:05,498 INFO Successfully created collection mem0migrations with dimension 768
2025-05-22 14:01:05,699 INFO Inserting 1 vectors into collection mem0migrations
2025-05-22 14:01:05,950 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-22 14:01:05,950 INFO Retrieving memories for user_id=user-7i6hu2vn, query='dfg'
2025-05-22 14:01:07,511 INFO Retrieved memory for user_id=user-7i6hu2vn, query='dfg': {'results': []}
2025-05-22 14:01:07,592 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:01:07,604 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:01:08,281 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:01:08,282 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=fe5129507bc94a228696f6aa59ebfc03
2025-05-22 14:01:08,282 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=fe5129507bc94a228696f6aa59ebfc03
2025-05-22 14:01:08,290 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=fe5129507bc94a228696f6aa59ebfc03 "HTTP/1.1 202 Accepted"
2025-05-22 14:01:08,292 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:01:08,868 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:01:08,869 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=29d7417fac3f42ffbab888c40b7f1b28
2025-05-22 14:01:08,869 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=29d7417fac3f42ffbab888c40b7f1b28
2025-05-22 14:01:08,875 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=29d7417fac3f42ffbab888c40b7f1b28 "HTTP/1.1 202 Accepted"
2025-05-22 14:01:08,890 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=fe5129507bc94a228696f6aa59ebfc03 "HTTP/1.1 202 Accepted"
2025-05-22 14:01:08,933 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=29d7417fac3f42ffbab888c40b7f1b28 "HTTP/1.1 202 Accepted"
2025-05-22 14:01:08,944 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=fe5129507bc94a228696f6aa59ebfc03 "HTTP/1.1 202 Accepted"
2025-05-22 14:01:08,986 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=29d7417fac3f42ffbab888c40b7f1b28 "HTTP/1.1 202 Accepted"
2025-05-22 14:01:10,283 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:03:44,400 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 14:03:45,517 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 14:03:45,519 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 14:03:45,524 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:03:45,547 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:04:21,566 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:04:24,819 INFO Creating new collection: mem0_memories768
2025-05-22 14:04:25,471 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-22 14:04:27,625 INFO Creating new collection: mem0migrations
2025-05-22 14:04:28,283 INFO Successfully created collection mem0migrations with dimension 768
2025-05-22 14:04:28,460 INFO Inserting 1 vectors into collection mem0migrations
2025-05-22 14:04:28,725 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-22 14:04:28,726 INFO Retrieving memories for user_id=user-hactt0ne, query='Check'
2025-05-22 14:04:30,142 INFO Retrieved memory for user_id=user-hactt0ne, query='Check': {'results': []}
2025-05-22 14:04:30,220 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:04:30,232 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:04:30,883 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:04:30,884 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=119cac2d932a4b5a8cc55642c8efd035
2025-05-22 14:04:30,884 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=119cac2d932a4b5a8cc55642c8efd035
2025-05-22 14:04:30,890 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=119cac2d932a4b5a8cc55642c8efd035 "HTTP/1.1 202 Accepted"
2025-05-22 14:04:30,892 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:04:31,437 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:04:31,438 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=7163e51a73894791ae5fb3c0c99bc557
2025-05-22 14:04:31,438 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=7163e51a73894791ae5fb3c0c99bc557
2025-05-22 14:04:31,445 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=7163e51a73894791ae5fb3c0c99bc557 "HTTP/1.1 202 Accepted"
2025-05-22 14:04:31,468 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=119cac2d932a4b5a8cc55642c8efd035 "HTTP/1.1 202 Accepted"
2025-05-22 14:04:31,510 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=7163e51a73894791ae5fb3c0c99bc557 "HTTP/1.1 202 Accepted"
2025-05-22 14:04:31,531 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=119cac2d932a4b5a8cc55642c8efd035 "HTTP/1.1 202 Accepted"
2025-05-22 14:04:31,563 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=7163e51a73894791ae5fb3c0c99bc557 "HTTP/1.1 202 Accepted"
2025-05-22 14:04:32,648 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:06:15,202 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:06:16,840 INFO Creating new collection: mem0_memories768
2025-05-22 14:06:17,534 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-22 14:06:19,540 INFO Creating new collection: mem0migrations
2025-05-22 14:06:20,168 INFO Successfully created collection mem0migrations with dimension 768
2025-05-22 14:06:20,393 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-22 14:06:20,394 INFO Retrieving memories for user_id=user-u8s59t55, query='hi'
2025-05-22 14:06:21,986 INFO Retrieved memory for user_id=user-u8s59t55, query='hi': {'results': []}
2025-05-22 14:06:22,052 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:06:22,061 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:06:22,573 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:06:22,574 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=2e0d2f451b8a498c89796bef38a2aba6
2025-05-22 14:06:22,574 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=2e0d2f451b8a498c89796bef38a2aba6
2025-05-22 14:06:22,580 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=2e0d2f451b8a498c89796bef38a2aba6 "HTTP/1.1 202 Accepted"
2025-05-22 14:06:22,582 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:06:23,150 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:06:23,150 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=ba334668e5f740d69357f9d23d91ca84
2025-05-22 14:06:23,151 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=ba334668e5f740d69357f9d23d91ca84
2025-05-22 14:06:23,157 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=ba334668e5f740d69357f9d23d91ca84 "HTTP/1.1 202 Accepted"
2025-05-22 14:06:23,170 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=2e0d2f451b8a498c89796bef38a2aba6 "HTTP/1.1 202 Accepted"
2025-05-22 14:06:23,223 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=ba334668e5f740d69357f9d23d91ca84 "HTTP/1.1 202 Accepted"
2025-05-22 14:06:23,234 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=2e0d2f451b8a498c89796bef38a2aba6 "HTTP/1.1 202 Accepted"
2025-05-22 14:06:23,276 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=ba334668e5f740d69357f9d23d91ca84 "HTTP/1.1 202 Accepted"
2025-05-22 14:06:24,387 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:06:26,110 INFO HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:batchEmbedContents "HTTP/1.1 200 OK"
2025-05-22 14:06:28,392 INFO HTTP Request: POST https://gttwzegcmextyljehehj.supabase.co/rest/v1/rpc/match_documents_768 "HTTP/2 200 OK"
2025-05-22 14:07:59,111 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 14:08:00,259 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 14:08:00,261 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 14:08:00,267 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:08:00,289 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:08:17,730 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:08:21,173 INFO Creating new collection: mem0_memories768
2025-05-22 14:08:21,821 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-22 14:08:23,910 INFO Creating new collection: mem0migrations
2025-05-22 14:08:24,543 INFO Successfully created collection mem0migrations with dimension 768
2025-05-22 14:08:24,749 INFO Inserting 1 vectors into collection mem0migrations
2025-05-22 14:08:25,033 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-22 14:08:25,034 INFO Retrieving memories for user_id=user-5xcwcvbn, query='Who are you ?'
2025-05-22 14:08:27,116 INFO Retrieved memory for user_id=user-5xcwcvbn, query='Who are you ?': {'results': []}
2025-05-22 14:08:27,218 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:08:27,234 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:08:28,001 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:08:28,002 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=f23fae2e061d48b1b6efa7d4dca061cd
2025-05-22 14:08:28,002 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=f23fae2e061d48b1b6efa7d4dca061cd
2025-05-22 14:08:28,010 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=f23fae2e061d48b1b6efa7d4dca061cd "HTTP/1.1 202 Accepted"
2025-05-22 14:08:28,011 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:08:28,598 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:08:28,598 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=94cc9a5e75cc40bdac6370c9fb16bfd6
2025-05-22 14:08:28,599 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=94cc9a5e75cc40bdac6370c9fb16bfd6
2025-05-22 14:08:28,605 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=94cc9a5e75cc40bdac6370c9fb16bfd6 "HTTP/1.1 202 Accepted"
2025-05-22 14:08:28,628 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=f23fae2e061d48b1b6efa7d4dca061cd "HTTP/1.1 202 Accepted"
2025-05-22 14:08:28,660 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=94cc9a5e75cc40bdac6370c9fb16bfd6 "HTTP/1.1 202 Accepted"
2025-05-22 14:08:28,680 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=f23fae2e061d48b1b6efa7d4dca061cd "HTTP/1.1 202 Accepted"
2025-05-22 14:08:28,711 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=94cc9a5e75cc40bdac6370c9fb16bfd6 "HTTP/1.1 202 Accepted"
2025-05-22 14:08:29,247 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:08:30,888 INFO Creating new collection: mem0_memories768
2025-05-22 14:08:31,509 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-22 14:08:33,583 INFO Creating new collection: mem0migrations
2025-05-22 14:08:34,265 INFO Successfully created collection mem0migrations with dimension 768
2025-05-22 14:08:34,503 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-22 14:08:34,503 INFO Adding memory for user_id=user-5xcwcvbn
2025-05-22 14:08:36,216 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:08:36,318 INFO Total existing memories: 0
2025-05-22 14:08:37,006 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:09:27,495 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:09:27,619 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:09:27,632 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:09:28,304 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:09:28,305 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=c9d1880542d24c87bfec14ef2f6b7ad4
2025-05-22 14:09:28,306 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=c9d1880542d24c87bfec14ef2f6b7ad4
2025-05-22 14:09:28,314 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=c9d1880542d24c87bfec14ef2f6b7ad4 "HTTP/1.1 202 Accepted"
2025-05-22 14:09:28,316 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:09:28,862 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:09:28,863 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=c292f7137f08471cbb78c1d1dbca2a09
2025-05-22 14:09:28,863 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=c292f7137f08471cbb78c1d1dbca2a09
2025-05-22 14:09:28,869 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c292f7137f08471cbb78c1d1dbca2a09 "HTTP/1.1 202 Accepted"
2025-05-22 14:09:28,885 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=c9d1880542d24c87bfec14ef2f6b7ad4 "HTTP/1.1 202 Accepted"
2025-05-22 14:09:28,917 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c292f7137f08471cbb78c1d1dbca2a09 "HTTP/1.1 202 Accepted"
2025-05-22 14:09:28,949 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=c9d1880542d24c87bfec14ef2f6b7ad4 "HTTP/1.1 202 Accepted"
2025-05-22 14:09:28,981 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c292f7137f08471cbb78c1d1dbca2a09 "HTTP/1.1 202 Accepted"
2025-05-22 14:09:29,156 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-22 14:10:51,818 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:10:51,905 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:10:51,918 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:10:52,516 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:10:52,517 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=b4384463153848f2b31b4ec900b41243
2025-05-22 14:10:52,517 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=b4384463153848f2b31b4ec900b41243
2025-05-22 14:10:52,524 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b4384463153848f2b31b4ec900b41243 "HTTP/1.1 202 Accepted"
2025-05-22 14:10:52,525 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:10:53,168 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:10:53,168 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=a625375776574cada0326380a94ea4cc
2025-05-22 14:10:53,169 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=a625375776574cada0326380a94ea4cc
2025-05-22 14:10:53,175 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b4384463153848f2b31b4ec900b41243 "HTTP/1.1 202 Accepted"
2025-05-22 14:10:53,192 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=a625375776574cada0326380a94ea4cc "HTTP/1.1 202 Accepted"
2025-05-22 14:10:53,249 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=a625375776574cada0326380a94ea4cc "HTTP/1.1 202 Accepted"
2025-05-22 14:10:53,250 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b4384463153848f2b31b4ec900b41243 "HTTP/1.1 202 Accepted"
2025-05-22 14:10:53,302 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=a625375776574cada0326380a94ea4cc "HTTP/1.1 202 Accepted"
2025-05-22 14:10:53,495 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-22 14:17:43,138 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:17:43,246 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:17:43,254 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:17:43,837 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:17:43,839 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=5cbf17b48f0e478eb5711badbbe72fcd
2025-05-22 14:17:43,839 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=5cbf17b48f0e478eb5711badbbe72fcd
2025-05-22 14:17:43,847 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=5cbf17b48f0e478eb5711badbbe72fcd "HTTP/1.1 202 Accepted"
2025-05-22 14:17:43,849 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:17:44,703 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:17:44,712 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=13ff25511cc64e6d8ba8e02839a82f15
2025-05-22 14:17:44,712 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=13ff25511cc64e6d8ba8e02839a82f15
2025-05-22 14:17:44,721 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=13ff25511cc64e6d8ba8e02839a82f15 "HTTP/1.1 202 Accepted"
2025-05-22 14:17:44,725 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=5cbf17b48f0e478eb5711badbbe72fcd "HTTP/1.1 202 Accepted"
2025-05-22 14:17:44,778 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=13ff25511cc64e6d8ba8e02839a82f15 "HTTP/1.1 202 Accepted"
2025-05-22 14:17:44,779 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=5cbf17b48f0e478eb5711badbbe72fcd "HTTP/1.1 202 Accepted"
2025-05-22 14:17:44,830 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=13ff25511cc64e6d8ba8e02839a82f15 "HTTP/1.1 202 Accepted"
2025-05-22 14:17:45,221 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-22 14:18:41,790 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:18:41,892 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:18:41,905 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:18:42,527 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:18:42,527 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=3325015e703141b0b629dcff586cf44a
2025-05-22 14:18:42,528 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=3325015e703141b0b629dcff586cf44a
2025-05-22 14:18:42,536 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=3325015e703141b0b629dcff586cf44a "HTTP/1.1 202 Accepted"
2025-05-22 14:18:42,538 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:18:43,152 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:18:43,153 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=fdc108eef8a740f6aaeab6e15e4cc257
2025-05-22 14:18:43,153 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=fdc108eef8a740f6aaeab6e15e4cc257
2025-05-22 14:18:43,158 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=3325015e703141b0b629dcff586cf44a "HTTP/1.1 202 Accepted"
2025-05-22 14:18:43,159 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=fdc108eef8a740f6aaeab6e15e4cc257 "HTTP/1.1 202 Accepted"
2025-05-22 14:18:43,222 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=fdc108eef8a740f6aaeab6e15e4cc257 "HTTP/1.1 202 Accepted"
2025-05-22 14:18:43,222 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=3325015e703141b0b629dcff586cf44a "HTTP/1.1 202 Accepted"
2025-05-22 14:18:43,274 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=fdc108eef8a740f6aaeab6e15e4cc257 "HTTP/1.1 202 Accepted"
2025-05-22 14:18:43,506 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-22 14:25:28,975 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 14:25:30,126 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 14:25:30,127 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 14:25:30,135 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:25:30,160 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:27:23,464 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:27:23,548 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:27:23,557 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:27:24,111 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:27:24,112 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=e6f5d5fa09c045759ad4c01085945fc4
2025-05-22 14:27:24,113 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=e6f5d5fa09c045759ad4c01085945fc4
2025-05-22 14:27:24,121 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e6f5d5fa09c045759ad4c01085945fc4 "HTTP/1.1 202 Accepted"
2025-05-22 14:27:24,124 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:27:24,859 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:27:24,859 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=7a648fe7e8e74e9097f88fa9e31728cd
2025-05-22 14:27:24,860 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=7a648fe7e8e74e9097f88fa9e31728cd
2025-05-22 14:27:24,868 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=7a648fe7e8e74e9097f88fa9e31728cd "HTTP/1.1 202 Accepted"
2025-05-22 14:27:24,880 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e6f5d5fa09c045759ad4c01085945fc4 "HTTP/1.1 202 Accepted"
2025-05-22 14:27:24,924 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=7a648fe7e8e74e9097f88fa9e31728cd "HTTP/1.1 202 Accepted"
2025-05-22 14:27:24,933 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e6f5d5fa09c045759ad4c01085945fc4 "HTTP/1.1 202 Accepted"
2025-05-22 14:27:24,977 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=7a648fe7e8e74e9097f88fa9e31728cd "HTTP/1.1 202 Accepted"
2025-05-22 14:27:25,243 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-22 14:30:19,396 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 14:30:20,450 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 14:30:20,451 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 14:30:20,457 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:30:20,475 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:30:35,236 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:30:35,318 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:30:35,327 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:30:35,920 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:30:35,921 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=dc07081c45324049816448e657c8d0e0
2025-05-22 14:30:35,921 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=dc07081c45324049816448e657c8d0e0
2025-05-22 14:30:35,929 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=dc07081c45324049816448e657c8d0e0 "HTTP/1.1 202 Accepted"
2025-05-22 14:30:35,931 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:30:36,602 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:30:36,603 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=979cb33bef2441f2aa6ea98ca2fbab8c
2025-05-22 14:30:36,604 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=979cb33bef2441f2aa6ea98ca2fbab8c
2025-05-22 14:30:36,611 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=979cb33bef2441f2aa6ea98ca2fbab8c "HTTP/1.1 202 Accepted"
2025-05-22 14:30:36,626 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=dc07081c45324049816448e657c8d0e0 "HTTP/1.1 202 Accepted"
2025-05-22 14:30:36,668 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=979cb33bef2441f2aa6ea98ca2fbab8c "HTTP/1.1 202 Accepted"
2025-05-22 14:30:36,680 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=dc07081c45324049816448e657c8d0e0 "HTTP/1.1 202 Accepted"
2025-05-22 14:30:36,732 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=979cb33bef2441f2aa6ea98ca2fbab8c "HTTP/1.1 202 Accepted"
2025-05-22 14:30:37,065 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-22 14:35:43,435 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 14:35:44,491 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 14:35:44,492 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 14:35:44,496 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:35:44,516 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:37:10,201 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:37:10,285 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:37:10,295 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:37:10,904 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:37:10,905 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=f831b8261e7f4c69bf130b4e1a6ce44f
2025-05-22 14:37:10,906 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=f831b8261e7f4c69bf130b4e1a6ce44f
2025-05-22 14:37:10,913 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=f831b8261e7f4c69bf130b4e1a6ce44f "HTTP/1.1 202 Accepted"
2025-05-22 14:37:10,915 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:37:11,546 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:37:11,547 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=5ff00acf79ad4c5fb1366d8e6635bac1
2025-05-22 14:37:11,548 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=5ff00acf79ad4c5fb1366d8e6635bac1
2025-05-22 14:37:11,555 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5ff00acf79ad4c5fb1366d8e6635bac1 "HTTP/1.1 202 Accepted"
2025-05-22 14:37:11,570 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=f831b8261e7f4c69bf130b4e1a6ce44f "HTTP/1.1 202 Accepted"
2025-05-22 14:37:11,612 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5ff00acf79ad4c5fb1366d8e6635bac1 "HTTP/1.1 202 Accepted"
2025-05-22 14:37:11,624 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=f831b8261e7f4c69bf130b4e1a6ce44f "HTTP/1.1 202 Accepted"
2025-05-22 14:37:11,666 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=5ff00acf79ad4c5fb1366d8e6635bac1 "HTTP/1.1 202 Accepted"
2025-05-22 14:37:12,755 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:37:15,663 INFO HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:batchEmbedContents "HTTP/1.1 200 OK"
2025-05-22 14:37:16,643 INFO HTTP Request: POST https://gttwzegcmextyljehehj.supabase.co/rest/v1/rpc/match_documents_768 "HTTP/2 200 OK"
2025-05-22 14:39:58,473 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:39:58,860 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:39:58,881 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:40:00,247 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:40:00,248 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=eeb2475d0f1741d1aee91fb6451a8967
2025-05-22 14:40:00,249 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=eeb2475d0f1741d1aee91fb6451a8967
2025-05-22 14:40:00,262 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=eeb2475d0f1741d1aee91fb6451a8967 "HTTP/1.1 202 Accepted"
2025-05-22 14:40:00,265 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:40:01,240 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=eeb2475d0f1741d1aee91fb6451a8967 "HTTP/1.1 202 Accepted"
2025-05-22 14:40:01,268 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:40:01,270 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=00dec43dc7da49cebef5be6cbf48dd8c
2025-05-22 14:40:01,281 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=00dec43dc7da49cebef5be6cbf48dd8c
2025-05-22 14:40:01,311 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=00dec43dc7da49cebef5be6cbf48dd8c "HTTP/1.1 202 Accepted"
2025-05-22 14:40:01,376 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=00dec43dc7da49cebef5be6cbf48dd8c "HTTP/1.1 202 Accepted"
2025-05-22 14:40:01,381 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=eeb2475d0f1741d1aee91fb6451a8967 "HTTP/1.1 202 Accepted"
2025-05-22 14:40:01,439 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=00dec43dc7da49cebef5be6cbf48dd8c "HTTP/1.1 202 Accepted"
2025-05-22 14:40:02,635 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:40:05,825 INFO HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:batchEmbedContents "HTTP/1.1 200 OK"
2025-05-22 14:40:07,860 INFO HTTP Request: POST https://gttwzegcmextyljehehj.supabase.co/rest/v1/rpc/match_documents_768 "HTTP/2 200 OK"
2025-05-22 14:40:33,679 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:40:37,770 INFO Creating new collection: mem0_memories768
2025-05-22 14:40:38,387 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-22 14:40:40,409 INFO Creating new collection: mem0migrations
2025-05-22 14:40:41,035 INFO Successfully created collection mem0migrations with dimension 768
2025-05-22 14:40:41,219 INFO Inserting 1 vectors into collection mem0migrations
2025-05-22 14:40:41,450 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-22 14:40:41,451 INFO Retrieving memories for user_id=user-5xcwcvbn, query='hi'
2025-05-22 14:40:43,932 INFO Retrieved memory for user_id=user-5xcwcvbn, query='hi': {'results': []}
2025-05-22 14:40:44,003 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:40:44,023 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:40:44,666 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:40:44,667 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=b9a83c87be7b459c806cd61f80837e3b
2025-05-22 14:40:44,667 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=b9a83c87be7b459c806cd61f80837e3b
2025-05-22 14:40:44,673 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b9a83c87be7b459c806cd61f80837e3b "HTTP/1.1 202 Accepted"
2025-05-22 14:40:44,675 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:40:45,236 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:40:45,237 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=05cbaf76792f4eab856771972b9b08e2
2025-05-22 14:40:45,237 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=05cbaf76792f4eab856771972b9b08e2
2025-05-22 14:40:45,245 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=05cbaf76792f4eab856771972b9b08e2 "HTTP/1.1 202 Accepted"
2025-05-22 14:40:45,260 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b9a83c87be7b459c806cd61f80837e3b "HTTP/1.1 202 Accepted"
2025-05-22 14:40:45,312 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b9a83c87be7b459c806cd61f80837e3b "HTTP/1.1 202 Accepted"
2025-05-22 14:40:45,313 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=05cbaf76792f4eab856771972b9b08e2 "HTTP/1.1 202 Accepted"
2025-05-22 14:40:45,365 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=05cbaf76792f4eab856771972b9b08e2 "HTTP/1.1 202 Accepted"
2025-05-22 14:40:45,982 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:40:47,572 INFO Creating new collection: mem0_memories768
2025-05-22 14:40:48,226 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-22 14:40:51,811 INFO Creating new collection: mem0migrations
2025-05-22 14:40:52,481 INFO Successfully created collection mem0migrations with dimension 768
2025-05-22 14:40:52,744 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-22 14:40:52,745 INFO Adding memory for user_id=user-5xcwcvbn
2025-05-22 14:40:54,667 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:40:54,813 INFO Total existing memories: 0
2025-05-22 14:40:55,562 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:41:06,577 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:41:06,669 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:41:06,678 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:41:07,325 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:41:07,326 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=434faec83fc4475b85ddc13dc32fdd9b
2025-05-22 14:41:07,326 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=434faec83fc4475b85ddc13dc32fdd9b
2025-05-22 14:41:07,332 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=434faec83fc4475b85ddc13dc32fdd9b "HTTP/1.1 202 Accepted"
2025-05-22 14:41:07,334 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:41:07,902 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:41:07,903 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=3b868adc409242028dca97a32eefcced
2025-05-22 14:41:07,903 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=3b868adc409242028dca97a32eefcced
2025-05-22 14:41:07,909 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=3b868adc409242028dca97a32eefcced "HTTP/1.1 202 Accepted"
2025-05-22 14:41:07,934 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=434faec83fc4475b85ddc13dc32fdd9b "HTTP/1.1 202 Accepted"
2025-05-22 14:41:07,977 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=3b868adc409242028dca97a32eefcced "HTTP/1.1 202 Accepted"
2025-05-22 14:41:07,988 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=434faec83fc4475b85ddc13dc32fdd9b "HTTP/1.1 202 Accepted"
2025-05-22 14:41:08,040 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=3b868adc409242028dca97a32eefcced "HTTP/1.1 202 Accepted"
2025-05-22 14:41:08,611 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:41:45,319 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:41:46,787 INFO Creating new collection: mem0_memories768
2025-05-22 14:41:47,400 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-22 14:41:49,412 INFO Creating new collection: mem0migrations
2025-05-22 14:41:50,049 INFO Successfully created collection mem0migrations with dimension 768
2025-05-22 14:41:50,276 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-22 14:41:50,276 INFO Retrieving memories for user_id=user-frqwr1ir, query='What does rule 100 say ?'
2025-05-22 14:41:51,715 INFO Retrieved memory for user_id=user-frqwr1ir, query='What does rule 100 say ?': {'results': []}
2025-05-22 14:41:51,781 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:41:51,790 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:41:52,344 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:41:52,344 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=4ef0280a49c24c86bf959214e93ca8de
2025-05-22 14:41:52,345 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=4ef0280a49c24c86bf959214e93ca8de
2025-05-22 14:41:52,352 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=4ef0280a49c24c86bf959214e93ca8de "HTTP/1.1 202 Accepted"
2025-05-22 14:41:52,353 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:41:52,911 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:41:52,912 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=d9858df346ad4afaba931e47c42df386
2025-05-22 14:41:52,913 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=d9858df346ad4afaba931e47c42df386
2025-05-22 14:41:52,931 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d9858df346ad4afaba931e47c42df386 "HTTP/1.1 202 Accepted"
2025-05-22 14:41:52,940 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=4ef0280a49c24c86bf959214e93ca8de "HTTP/1.1 202 Accepted"
2025-05-22 14:41:52,993 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d9858df346ad4afaba931e47c42df386 "HTTP/1.1 202 Accepted"
2025-05-22 14:41:53,004 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=4ef0280a49c24c86bf959214e93ca8de "HTTP/1.1 202 Accepted"
2025-05-22 14:41:53,047 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d9858df346ad4afaba931e47c42df386 "HTTP/1.1 202 Accepted"
2025-05-22 14:41:54,098 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:41:54,287 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=4ef0280a49c24c86bf959214e93ca8de "HTTP/1.1 202 Accepted"
2025-05-22 14:41:54,350 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d9858df346ad4afaba931e47c42df386 "HTTP/1.1 202 Accepted"
2025-05-22 14:41:54,414 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d9858df346ad4afaba931e47c42df386 "HTTP/1.1 202 Accepted"
2025-05-22 14:41:55,675 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=4ef0280a49c24c86bf959214e93ca8de "HTTP/1.1 202 Accepted"
2025-05-22 14:41:55,676 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=d9858df346ad4afaba931e47c42df386 "HTTP/1.1 202 Accepted"
2025-05-22 14:41:56,542 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:41:58,291 INFO Creating new collection: mem0_memories768
2025-05-22 14:41:58,941 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-22 14:42:01,090 INFO Creating new collection: mem0migrations
2025-05-22 14:42:01,706 INFO Successfully created collection mem0migrations with dimension 768
2025-05-22 14:42:01,932 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-22 14:42:01,932 INFO Adding memory for user_id=user-frqwr1ir
2025-05-22 14:42:03,264 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:42:03,445 INFO Total existing memories: 0
2025-05-22 14:42:04,021 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:42:32,394 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:42:32,467 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:42:32,475 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:42:33,046 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:42:33,047 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=44e1520164b046df8675001934b15268
2025-05-22 14:42:33,047 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=44e1520164b046df8675001934b15268
2025-05-22 14:42:33,053 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=44e1520164b046df8675001934b15268 "HTTP/1.1 202 Accepted"
2025-05-22 14:42:33,054 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:42:33,589 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:42:33,590 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=6d62665eb779421b96f5f9221c36784e
2025-05-22 14:42:33,590 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=6d62665eb779421b96f5f9221c36784e
2025-05-22 14:42:33,596 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6d62665eb779421b96f5f9221c36784e "HTTP/1.1 202 Accepted"
2025-05-22 14:42:33,615 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=44e1520164b046df8675001934b15268 "HTTP/1.1 202 Accepted"
2025-05-22 14:42:33,647 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6d62665eb779421b96f5f9221c36784e "HTTP/1.1 202 Accepted"
2025-05-22 14:42:33,668 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=44e1520164b046df8675001934b15268 "HTTP/1.1 202 Accepted"
2025-05-22 14:42:33,700 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6d62665eb779421b96f5f9221c36784e "HTTP/1.1 202 Accepted"
2025-05-22 14:42:34,728 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:43:15,505 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:43:15,586 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:43:15,596 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:43:16,183 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:43:16,184 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=f1dbf8cba01241e8a859d3d1ba09281a
2025-05-22 14:43:16,184 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=f1dbf8cba01241e8a859d3d1ba09281a
2025-05-22 14:43:16,190 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=f1dbf8cba01241e8a859d3d1ba09281a "HTTP/1.1 202 Accepted"
2025-05-22 14:43:16,191 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:43:16,796 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:43:16,797 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=a8e28130103b4a74bef3dead06fdc93f
2025-05-22 14:43:16,797 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=a8e28130103b4a74bef3dead06fdc93f
2025-05-22 14:43:16,803 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=a8e28130103b4a74bef3dead06fdc93f "HTTP/1.1 202 Accepted"
2025-05-22 14:43:16,806 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=f1dbf8cba01241e8a859d3d1ba09281a "HTTP/1.1 202 Accepted"
2025-05-22 14:43:16,857 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=a8e28130103b4a74bef3dead06fdc93f "HTTP/1.1 202 Accepted"
2025-05-22 14:43:16,868 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=f1dbf8cba01241e8a859d3d1ba09281a "HTTP/1.1 202 Accepted"
2025-05-22 14:43:16,911 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=a8e28130103b4a74bef3dead06fdc93f "HTTP/1.1 202 Accepted"
2025-05-22 14:43:17,884 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:43:37,113 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:43:37,192 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:43:37,200 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:43:37,763 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:43:37,764 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=e04a9450d9454ada96fe100c6810c572
2025-05-22 14:43:37,764 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=e04a9450d9454ada96fe100c6810c572
2025-05-22 14:43:37,774 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e04a9450d9454ada96fe100c6810c572 "HTTP/1.1 202 Accepted"
2025-05-22 14:43:37,775 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:43:38,328 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:43:38,329 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=8d5b142204414651b5944963f760b4ab
2025-05-22 14:43:38,329 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=8d5b142204414651b5944963f760b4ab
2025-05-22 14:43:38,336 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=8d5b142204414651b5944963f760b4ab "HTTP/1.1 202 Accepted"
2025-05-22 14:43:38,349 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e04a9450d9454ada96fe100c6810c572 "HTTP/1.1 202 Accepted"
2025-05-22 14:43:38,402 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=8d5b142204414651b5944963f760b4ab "HTTP/1.1 202 Accepted"
2025-05-22 14:43:38,403 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e04a9450d9454ada96fe100c6810c572 "HTTP/1.1 202 Accepted"
2025-05-22 14:43:38,455 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=8d5b142204414651b5944963f760b4ab "HTTP/1.1 202 Accepted"
2025-05-22 14:43:39,494 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:48:01,795 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 14:48:02,955 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 14:48:02,956 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 14:48:02,962 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:48:02,988 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:48:12,590 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:48:12,669 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:48:12,680 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:48:13,264 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:48:13,264 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=e7756db2606b42b98ed81abee9d1038a
2025-05-22 14:48:13,265 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=e7756db2606b42b98ed81abee9d1038a
2025-05-22 14:48:13,271 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e7756db2606b42b98ed81abee9d1038a "HTTP/1.1 202 Accepted"
2025-05-22 14:48:13,273 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:48:13,841 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:48:13,842 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=869459685e2a490fb55ffc032f376d5c
2025-05-22 14:48:13,843 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=869459685e2a490fb55ffc032f376d5c
2025-05-22 14:48:13,849 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=869459685e2a490fb55ffc032f376d5c "HTTP/1.1 202 Accepted"
2025-05-22 14:48:13,867 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e7756db2606b42b98ed81abee9d1038a "HTTP/1.1 202 Accepted"
2025-05-22 14:48:13,909 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=869459685e2a490fb55ffc032f376d5c "HTTP/1.1 202 Accepted"
2025-05-22 14:48:13,930 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e7756db2606b42b98ed81abee9d1038a "HTTP/1.1 202 Accepted"
2025-05-22 14:48:13,963 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=869459685e2a490fb55ffc032f376d5c "HTTP/1.1 202 Accepted"
2025-05-22 14:48:15,671 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:48:29,566 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:48:29,653 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 14:48:29,664 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 14:48:30,225 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 14:48:30,226 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=595ff576612a41a2929df0a5969e062a
2025-05-22 14:48:30,226 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=595ff576612a41a2929df0a5969e062a
2025-05-22 14:48:30,232 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=595ff576612a41a2929df0a5969e062a "HTTP/1.1 202 Accepted"
2025-05-22 14:48:30,233 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 14:48:30,768 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 14:48:30,768 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=b91d74ee14bf442faf2363aa70ec1448
2025-05-22 14:48:30,769 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=b91d74ee14bf442faf2363aa70ec1448
2025-05-22 14:48:30,775 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=b91d74ee14bf442faf2363aa70ec1448 "HTTP/1.1 202 Accepted"
2025-05-22 14:48:30,802 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=595ff576612a41a2929df0a5969e062a "HTTP/1.1 202 Accepted"
2025-05-22 14:48:30,824 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=b91d74ee14bf442faf2363aa70ec1448 "HTTP/1.1 202 Accepted"
2025-05-22 14:48:30,866 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=595ff576612a41a2929df0a5969e062a "HTTP/1.1 202 Accepted"
2025-05-22 14:48:30,876 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=b91d74ee14bf442faf2363aa70ec1448 "HTTP/1.1 202 Accepted"
2025-05-22 14:48:31,928 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 14:48:32,019 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=595ff576612a41a2929df0a5969e062a "HTTP/1.1 202 Accepted"
2025-05-22 14:48:32,072 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=b91d74ee14bf442faf2363aa70ec1448 "HTTP/1.1 202 Accepted"
2025-05-22 14:48:32,136 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=b91d74ee14bf442faf2363aa70ec1448 "HTTP/1.1 202 Accepted"
2025-05-22 14:48:32,968 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=595ff576612a41a2929df0a5969e062a "HTTP/1.1 202 Accepted"
2025-05-22 14:48:32,969 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=b91d74ee14bf442faf2363aa70ec1448 "HTTP/1.1 202 Accepted"
2025-05-22 14:48:33,835 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 15:44:09,407 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 15:44:09,474 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 15:44:09,483 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 15:44:10,004 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 15:44:10,005 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=9501e06ce931418f920b2b00dd43eff3
2025-05-22 15:44:10,005 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=9501e06ce931418f920b2b00dd43eff3
2025-05-22 15:44:10,011 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9501e06ce931418f920b2b00dd43eff3 "HTTP/1.1 202 Accepted"
2025-05-22 15:44:10,013 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 15:44:10,585 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 15:44:10,586 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=448b13cf3dd447ba91bbee716e0af70e
2025-05-22 15:44:10,587 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=448b13cf3dd447ba91bbee716e0af70e
2025-05-22 15:44:10,592 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=448b13cf3dd447ba91bbee716e0af70e "HTTP/1.1 202 Accepted"
2025-05-22 15:44:10,606 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9501e06ce931418f920b2b00dd43eff3 "HTTP/1.1 202 Accepted"
2025-05-22 15:44:10,649 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=448b13cf3dd447ba91bbee716e0af70e "HTTP/1.1 202 Accepted"
2025-05-22 15:44:10,670 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9501e06ce931418f920b2b00dd43eff3 "HTTP/1.1 202 Accepted"
2025-05-22 15:44:10,703 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=448b13cf3dd447ba91bbee716e0af70e "HTTP/1.1 202 Accepted"
2025-05-22 15:44:11,805 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 15:44:29,100 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 15:44:29,223 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 15:44:29,234 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 15:44:29,855 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 15:44:29,856 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=5b63be53ad18428980d3a13b98525a5a
2025-05-22 15:44:29,857 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=5b63be53ad18428980d3a13b98525a5a
2025-05-22 15:44:29,862 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=5b63be53ad18428980d3a13b98525a5a "HTTP/1.1 202 Accepted"
2025-05-22 15:44:29,864 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 15:44:30,410 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 15:44:30,411 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=f28665b714c44c2b89a439d0a58dbaef
2025-05-22 15:44:30,411 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=f28665b714c44c2b89a439d0a58dbaef
2025-05-22 15:44:30,417 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f28665b714c44c2b89a439d0a58dbaef "HTTP/1.1 202 Accepted"
2025-05-22 15:44:30,446 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=5b63be53ad18428980d3a13b98525a5a "HTTP/1.1 202 Accepted"
2025-05-22 15:44:30,478 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f28665b714c44c2b89a439d0a58dbaef "HTTP/1.1 202 Accepted"
2025-05-22 15:44:30,510 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=5b63be53ad18428980d3a13b98525a5a "HTTP/1.1 202 Accepted"
2025-05-22 15:44:30,531 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f28665b714c44c2b89a439d0a58dbaef "HTTP/1.1 202 Accepted"
2025-05-22 15:44:31,566 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 15:45:18,135 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 15:45:19,388 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 15:45:19,389 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 15:45:19,394 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 15:45:19,425 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 15:49:52,901 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 15:49:56,559 INFO Creating new collection: mem0_memories768
2025-05-22 15:49:57,272 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-22 15:49:59,334 INFO Creating new collection: mem0migrations
2025-05-22 15:49:59,950 INFO Successfully created collection mem0migrations with dimension 768
2025-05-22 15:50:00,130 INFO Inserting 1 vectors into collection mem0migrations
2025-05-22 15:50:00,362 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-22 15:50:00,362 INFO Retrieving memories for user_id=user-3zb5qhgw, query='What does rule 69 say ?'
2025-05-22 15:50:02,044 INFO Retrieved memory for user_id=user-3zb5qhgw, query='What does rule 69 say ?': {'results': []}
2025-05-22 15:50:02,106 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 15:50:02,115 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 15:50:02,652 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 15:50:02,653 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=b7da70c88bb9421f876a9eee4cc966cc
2025-05-22 15:50:02,653 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=b7da70c88bb9421f876a9eee4cc966cc
2025-05-22 15:50:02,659 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b7da70c88bb9421f876a9eee4cc966cc "HTTP/1.1 202 Accepted"
2025-05-22 15:50:02,661 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 15:50:03,266 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 15:50:03,267 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=f40d4fc9f7414da080304e7e9e6686dd
2025-05-22 15:50:03,267 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=f40d4fc9f7414da080304e7e9e6686dd
2025-05-22 15:50:03,273 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b7da70c88bb9421f876a9eee4cc966cc "HTTP/1.1 202 Accepted"
2025-05-22 15:50:03,274 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f40d4fc9f7414da080304e7e9e6686dd "HTTP/1.1 202 Accepted"
2025-05-22 15:50:03,336 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f40d4fc9f7414da080304e7e9e6686dd "HTTP/1.1 202 Accepted"
2025-05-22 15:50:03,337 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b7da70c88bb9421f876a9eee4cc966cc "HTTP/1.1 202 Accepted"
2025-05-22 15:50:03,389 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f40d4fc9f7414da080304e7e9e6686dd "HTTP/1.1 202 Accepted"
2025-05-22 15:50:04,558 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:06:58,981 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 16:07:00,451 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 16:07:00,453 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 16:07:00,461 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:07:00,490 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:07:32,544 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:07:34,108 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:07:34,127 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 16:07:34,795 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:07:34,796 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=e037240b8c9e4810b90d363df4d2743c
2025-05-22 16:07:34,796 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=e037240b8c9e4810b90d363df4d2743c
2025-05-22 16:07:34,804 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e037240b8c9e4810b90d363df4d2743c "HTTP/1.1 202 Accepted"
2025-05-22 16:07:34,806 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 16:07:35,697 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:07:35,698 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=2f7d484bbfa042449acbbf20d56896e7
2025-05-22 16:07:35,698 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=2f7d484bbfa042449acbbf20d56896e7
2025-05-22 16:07:35,705 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2f7d484bbfa042449acbbf20d56896e7 "HTTP/1.1 202 Accepted"
2025-05-22 16:07:35,735 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e037240b8c9e4810b90d363df4d2743c "HTTP/1.1 202 Accepted"
2025-05-22 16:07:35,767 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2f7d484bbfa042449acbbf20d56896e7 "HTTP/1.1 202 Accepted"
2025-05-22 16:07:35,800 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e037240b8c9e4810b90d363df4d2743c "HTTP/1.1 202 Accepted"
2025-05-22 16:07:35,819 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2f7d484bbfa042449acbbf20d56896e7 "HTTP/1.1 202 Accepted"
2025-05-22 16:07:37,096 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:07:37,203 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e037240b8c9e4810b90d363df4d2743c "HTTP/1.1 202 Accepted"
2025-05-22 16:07:37,256 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2f7d484bbfa042449acbbf20d56896e7 "HTTP/1.1 202 Accepted"
2025-05-22 16:07:37,309 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2f7d484bbfa042449acbbf20d56896e7 "HTTP/1.1 202 Accepted"
2025-05-22 16:07:38,144 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e037240b8c9e4810b90d363df4d2743c "HTTP/1.1 202 Accepted"
2025-05-22 16:07:38,145 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2f7d484bbfa042449acbbf20d56896e7 "HTTP/1.1 202 Accepted"
2025-05-22 16:07:39,091 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:08:28,371 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 16:08:30,278 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 16:08:30,280 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 16:08:30,285 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:08:30,333 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:15:16,129 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:15:17,513 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:15:17,522 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 16:15:18,131 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:15:18,132 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=aa0bf857346c425fbe7c899c6fa14292
2025-05-22 16:15:18,133 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=aa0bf857346c425fbe7c899c6fa14292
2025-05-22 16:15:18,139 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=aa0bf857346c425fbe7c899c6fa14292 "HTTP/1.1 202 Accepted"
2025-05-22 16:15:18,141 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 16:15:18,722 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:15:18,723 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=9061fe6b4f2447968e2884d3f5f17088
2025-05-22 16:15:18,723 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=9061fe6b4f2447968e2884d3f5f17088
2025-05-22 16:15:18,729 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=9061fe6b4f2447968e2884d3f5f17088 "HTTP/1.1 202 Accepted"
2025-05-22 16:15:18,741 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=aa0bf857346c425fbe7c899c6fa14292 "HTTP/1.1 202 Accepted"
2025-05-22 16:15:18,786 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=9061fe6b4f2447968e2884d3f5f17088 "HTTP/1.1 202 Accepted"
2025-05-22 16:15:18,806 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=aa0bf857346c425fbe7c899c6fa14292 "HTTP/1.1 202 Accepted"
2025-05-22 16:15:18,840 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=9061fe6b4f2447968e2884d3f5f17088 "HTTP/1.1 202 Accepted"
2025-05-22 16:15:20,110 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:15:22,002 INFO HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:batchEmbedContents "HTTP/1.1 200 OK"
2025-05-22 16:15:23,535 INFO HTTP Request: POST https://gttwzegcmextyljehehj.supabase.co/rest/v1/rpc/match_documents_768 "HTTP/2 200 OK"
2025-05-22 16:21:48,732 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 16:21:49,768 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 16:21:49,769 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 16:21:49,787 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:21:49,793 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:22:42,934 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:22:44,207 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:22:44,215 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 16:22:44,741 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:22:44,742 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=43df9a3374df48f1a5d47fde8b036a1e
2025-05-22 16:22:44,742 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=43df9a3374df48f1a5d47fde8b036a1e
2025-05-22 16:22:44,749 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=43df9a3374df48f1a5d47fde8b036a1e "HTTP/1.1 202 Accepted"
2025-05-22 16:22:44,751 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 16:22:45,300 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:22:45,301 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=f7ff4fb44cdb4c2f8335cf798e74cb59
2025-05-22 16:22:45,301 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=f7ff4fb44cdb4c2f8335cf798e74cb59
2025-05-22 16:22:45,310 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f7ff4fb44cdb4c2f8335cf798e74cb59 "HTTP/1.1 202 Accepted"
2025-05-22 16:22:45,333 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=43df9a3374df48f1a5d47fde8b036a1e "HTTP/1.1 202 Accepted"
2025-05-22 16:22:45,376 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f7ff4fb44cdb4c2f8335cf798e74cb59 "HTTP/1.1 202 Accepted"
2025-05-22 16:22:45,398 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=43df9a3374df48f1a5d47fde8b036a1e "HTTP/1.1 202 Accepted"
2025-05-22 16:22:45,440 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=f7ff4fb44cdb4c2f8335cf798e74cb59 "HTTP/1.1 202 Accepted"
2025-05-22 16:22:46,764 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:27:55,680 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 16:27:56,806 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 16:27:56,807 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 16:27:56,813 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:27:56,840 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:28:14,054 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 16:28:15,261 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 16:28:15,262 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 16:28:15,268 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:28:15,291 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:30:37,751 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 16:30:38,836 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 16:30:38,837 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 16:30:38,842 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:30:38,864 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:30:39,580 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:30:40,736 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:30:40,744 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 16:30:41,308 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:30:41,309 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=d9bf304ce8534cc1b5037ac7fc86e7b2
2025-05-22 16:30:41,309 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=d9bf304ce8534cc1b5037ac7fc86e7b2
2025-05-22 16:30:41,315 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d9bf304ce8534cc1b5037ac7fc86e7b2 "HTTP/1.1 202 Accepted"
2025-05-22 16:30:41,316 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 16:30:41,898 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:30:41,899 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=e34ae2714bc842be8ca1859581a1054d
2025-05-22 16:30:41,899 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=e34ae2714bc842be8ca1859581a1054d
2025-05-22 16:30:41,906 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e34ae2714bc842be8ca1859581a1054d "HTTP/1.1 202 Accepted"
2025-05-22 16:30:41,925 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d9bf304ce8534cc1b5037ac7fc86e7b2 "HTTP/1.1 202 Accepted"
2025-05-22 16:30:41,968 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e34ae2714bc842be8ca1859581a1054d "HTTP/1.1 202 Accepted"
2025-05-22 16:30:41,989 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d9bf304ce8534cc1b5037ac7fc86e7b2 "HTTP/1.1 202 Accepted"
2025-05-22 16:30:42,021 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e34ae2714bc842be8ca1859581a1054d "HTTP/1.1 202 Accepted"
2025-05-22 16:30:46,896 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:30:51,790 INFO HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:batchEmbedContents "HTTP/1.1 200 OK"
2025-05-22 16:30:53,571 INFO HTTP Request: POST https://gttwzegcmextyljehehj.supabase.co/rest/v1/rpc/match_documents_768 "HTTP/2 200 OK"
2025-05-22 16:31:08,704 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:31:10,470 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:31:10,487 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 16:31:11,555 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:31:11,556 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=86b8392bf5a84d6eb60398d1ef11d38f
2025-05-22 16:31:11,556 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=86b8392bf5a84d6eb60398d1ef11d38f
2025-05-22 16:31:11,565 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=86b8392bf5a84d6eb60398d1ef11d38f "HTTP/1.1 202 Accepted"
2025-05-22 16:31:11,566 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 16:31:12,178 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:31:12,179 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=c12e7e76afa74a3c9854f4588ebcbd65
2025-05-22 16:31:12,179 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=c12e7e76afa74a3c9854f4588ebcbd65
2025-05-22 16:31:12,197 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c12e7e76afa74a3c9854f4588ebcbd65 "HTTP/1.1 202 Accepted"
2025-05-22 16:31:12,203 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=86b8392bf5a84d6eb60398d1ef11d38f "HTTP/1.1 202 Accepted"
2025-05-22 16:31:12,246 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c12e7e76afa74a3c9854f4588ebcbd65 "HTTP/1.1 202 Accepted"
2025-05-22 16:31:12,257 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=86b8392bf5a84d6eb60398d1ef11d38f "HTTP/1.1 202 Accepted"
2025-05-22 16:31:12,299 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c12e7e76afa74a3c9854f4588ebcbd65 "HTTP/1.1 202 Accepted"
2025-05-22 16:31:16,632 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:31:17,033 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=86b8392bf5a84d6eb60398d1ef11d38f "HTTP/1.1 202 Accepted"
2025-05-22 16:31:17,097 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c12e7e76afa74a3c9854f4588ebcbd65 "HTTP/1.1 202 Accepted"
2025-05-22 16:31:17,151 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c12e7e76afa74a3c9854f4588ebcbd65 "HTTP/1.1 202 Accepted"
2025-05-22 16:31:39,921 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:31:41,119 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:31:41,128 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 16:31:41,651 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:31:41,652 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=3353697df34740c685e5602f6f755fcb
2025-05-22 16:31:41,652 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=3353697df34740c685e5602f6f755fcb
2025-05-22 16:31:41,658 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=3353697df34740c685e5602f6f755fcb "HTTP/1.1 202 Accepted"
2025-05-22 16:31:41,659 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 16:31:42,237 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:31:42,238 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=bbc14959c39d4e7bbb23002f76a691f1
2025-05-22 16:31:42,238 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=bbc14959c39d4e7bbb23002f76a691f1
2025-05-22 16:31:42,244 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=bbc14959c39d4e7bbb23002f76a691f1 "HTTP/1.1 202 Accepted"
2025-05-22 16:31:42,250 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=3353697df34740c685e5602f6f755fcb "HTTP/1.1 202 Accepted"
2025-05-22 16:31:42,293 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=bbc14959c39d4e7bbb23002f76a691f1 "HTTP/1.1 202 Accepted"
2025-05-22 16:31:42,303 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=3353697df34740c685e5602f6f755fcb "HTTP/1.1 202 Accepted"
2025-05-22 16:31:42,346 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=bbc14959c39d4e7bbb23002f76a691f1 "HTTP/1.1 202 Accepted"
2025-05-22 16:31:46,260 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:31:47,315 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=3353697df34740c685e5602f6f755fcb "HTTP/1.1 202 Accepted"
2025-05-22 16:31:47,381 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=bbc14959c39d4e7bbb23002f76a691f1 "HTTP/1.1 202 Accepted"
2025-05-22 16:31:47,436 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=bbc14959c39d4e7bbb23002f76a691f1 "HTTP/1.1 202 Accepted"
2025-05-22 16:32:43,873 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:32:45,004 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:32:45,014 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 16:32:45,526 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:32:45,527 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=877999d9ad3949e891667542c00b8259
2025-05-22 16:32:45,527 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=877999d9ad3949e891667542c00b8259
2025-05-22 16:32:45,534 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=877999d9ad3949e891667542c00b8259 "HTTP/1.1 202 Accepted"
2025-05-22 16:32:45,535 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 16:32:46,072 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:32:46,073 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=c23ae924ea374eec8c378d5c1c0f2a95
2025-05-22 16:32:46,073 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=c23ae924ea374eec8c378d5c1c0f2a95
2025-05-22 16:32:46,079 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c23ae924ea374eec8c378d5c1c0f2a95 "HTTP/1.1 202 Accepted"
2025-05-22 16:32:46,096 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=877999d9ad3949e891667542c00b8259 "HTTP/1.1 202 Accepted"
2025-05-22 16:32:46,138 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c23ae924ea374eec8c378d5c1c0f2a95 "HTTP/1.1 202 Accepted"
2025-05-22 16:32:46,149 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=877999d9ad3949e891667542c00b8259 "HTTP/1.1 202 Accepted"
2025-05-22 16:32:46,192 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c23ae924ea374eec8c378d5c1c0f2a95 "HTTP/1.1 202 Accepted"
2025-05-22 16:32:48,210 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:32:49,017 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=877999d9ad3949e891667542c00b8259 "HTTP/1.1 202 Accepted"
2025-05-22 16:32:49,081 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c23ae924ea374eec8c378d5c1c0f2a95 "HTTP/1.1 202 Accepted"
2025-05-22 16:32:49,135 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=c23ae924ea374eec8c378d5c1c0f2a95 "HTTP/1.1 202 Accepted"
2025-05-22 16:33:30,716 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:33:32,382 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:33:32,391 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 16:33:32,958 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:33:32,958 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=a8391e69e3054525a001899085ce37cc
2025-05-22 16:33:32,959 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=a8391e69e3054525a001899085ce37cc
2025-05-22 16:33:32,964 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a8391e69e3054525a001899085ce37cc "HTTP/1.1 202 Accepted"
2025-05-22 16:33:32,966 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 16:33:33,556 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:33:33,557 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=596cedf8e6af424ca8778e473b06389e
2025-05-22 16:33:33,557 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=596cedf8e6af424ca8778e473b06389e
2025-05-22 16:33:33,564 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=596cedf8e6af424ca8778e473b06389e "HTTP/1.1 202 Accepted"
2025-05-22 16:33:33,594 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a8391e69e3054525a001899085ce37cc "HTTP/1.1 202 Accepted"
2025-05-22 16:33:33,625 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=596cedf8e6af424ca8778e473b06389e "HTTP/1.1 202 Accepted"
2025-05-22 16:33:33,647 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a8391e69e3054525a001899085ce37cc "HTTP/1.1 202 Accepted"
2025-05-22 16:33:33,679 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=596cedf8e6af424ca8778e473b06389e "HTTP/1.1 202 Accepted"
2025-05-22 16:33:37,780 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:33:38,772 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=a8391e69e3054525a001899085ce37cc "HTTP/1.1 202 Accepted"
2025-05-22 16:33:38,779 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=596cedf8e6af424ca8778e473b06389e "HTTP/1.1 202 Accepted"
2025-05-22 16:33:38,829 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=596cedf8e6af424ca8778e473b06389e "HTTP/1.1 202 Accepted"
2025-05-22 16:35:06,594 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:35:08,340 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:35:08,354 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 16:35:10,332 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:35:10,333 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=17aed436b21d4317ac6f9449fb310ffe
2025-05-22 16:35:10,333 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=17aed436b21d4317ac6f9449fb310ffe
2025-05-22 16:35:10,341 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=17aed436b21d4317ac6f9449fb310ffe "HTTP/1.1 202 Accepted"
2025-05-22 16:35:10,342 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 16:35:11,147 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:35:11,148 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=1d048736605847bf9fdc2ba3f794c33d
2025-05-22 16:35:11,148 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=1d048736605847bf9fdc2ba3f794c33d
2025-05-22 16:35:11,156 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=1d048736605847bf9fdc2ba3f794c33d "HTTP/1.1 202 Accepted"
2025-05-22 16:35:11,178 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=17aed436b21d4317ac6f9449fb310ffe "HTTP/1.1 202 Accepted"
2025-05-22 16:35:11,220 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=1d048736605847bf9fdc2ba3f794c33d "HTTP/1.1 202 Accepted"
2025-05-22 16:35:11,231 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=17aed436b21d4317ac6f9449fb310ffe "HTTP/1.1 202 Accepted"
2025-05-22 16:35:11,274 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=1d048736605847bf9fdc2ba3f794c33d "HTTP/1.1 202 Accepted"
2025-05-22 16:35:12,498 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:35:24,037 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:35:25,212 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:35:25,219 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 16:35:25,774 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:35:25,775 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=88a3c47a48e241489d7d7127f60ca9c7
2025-05-22 16:35:25,775 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=88a3c47a48e241489d7d7127f60ca9c7
2025-05-22 16:35:25,782 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=88a3c47a48e241489d7d7127f60ca9c7 "HTTP/1.1 202 Accepted"
2025-05-22 16:35:25,784 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 16:35:26,391 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:35:26,392 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=2d55be0be0a44fd2bde0759e91fe760e
2025-05-22 16:35:26,392 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=2d55be0be0a44fd2bde0759e91fe760e
2025-05-22 16:35:26,399 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2d55be0be0a44fd2bde0759e91fe760e "HTTP/1.1 202 Accepted"
2025-05-22 16:35:26,426 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=88a3c47a48e241489d7d7127f60ca9c7 "HTTP/1.1 202 Accepted"
2025-05-22 16:35:26,447 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2d55be0be0a44fd2bde0759e91fe760e "HTTP/1.1 202 Accepted"
2025-05-22 16:35:26,490 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=88a3c47a48e241489d7d7127f60ca9c7 "HTTP/1.1 202 Accepted"
2025-05-22 16:35:26,511 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2d55be0be0a44fd2bde0759e91fe760e "HTTP/1.1 202 Accepted"
2025-05-22 16:35:27,625 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:35:27,743 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=88a3c47a48e241489d7d7127f60ca9c7 "HTTP/1.1 202 Accepted"
2025-05-22 16:35:27,807 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2d55be0be0a44fd2bde0759e91fe760e "HTTP/1.1 202 Accepted"
2025-05-22 16:35:27,860 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2d55be0be0a44fd2bde0759e91fe760e "HTTP/1.1 202 Accepted"
2025-05-22 16:35:29,007 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2d55be0be0a44fd2bde0759e91fe760e "HTTP/1.1 202 Accepted"
2025-05-22 16:35:29,008 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=88a3c47a48e241489d7d7127f60ca9c7 "HTTP/1.1 202 Accepted"
2025-05-22 16:35:29,977 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:41:11,621 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:41:12,790 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:41:12,801 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 16:41:13,330 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:41:13,331 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=6a2b98424e14429db6008b5ca9de033a
2025-05-22 16:41:13,331 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=6a2b98424e14429db6008b5ca9de033a
2025-05-22 16:41:13,337 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=6a2b98424e14429db6008b5ca9de033a "HTTP/1.1 202 Accepted"
2025-05-22 16:41:13,338 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 16:41:13,925 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:41:13,926 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=4772a1f61b67497385b3423acc69efad
2025-05-22 16:41:13,926 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=4772a1f61b67497385b3423acc69efad
2025-05-22 16:41:13,933 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=4772a1f61b67497385b3423acc69efad "HTTP/1.1 202 Accepted"
2025-05-22 16:41:13,961 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=6a2b98424e14429db6008b5ca9de033a "HTTP/1.1 202 Accepted"
2025-05-22 16:41:13,993 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=4772a1f61b67497385b3423acc69efad "HTTP/1.1 202 Accepted"
2025-05-22 16:41:14,025 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=6a2b98424e14429db6008b5ca9de033a "HTTP/1.1 202 Accepted"
2025-05-22 16:41:14,056 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=4772a1f61b67497385b3423acc69efad "HTTP/1.1 202 Accepted"
2025-05-22 16:41:17,463 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:41:18,163 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=6a2b98424e14429db6008b5ca9de033a "HTTP/1.1 202 Accepted"
2025-05-22 16:41:18,227 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=4772a1f61b67497385b3423acc69efad "HTTP/1.1 202 Accepted"
2025-05-22 16:41:18,279 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=4772a1f61b67497385b3423acc69efad "HTTP/1.1 202 Accepted"
2025-05-22 16:42:04,951 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:42:06,331 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:42:06,343 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 16:42:06,917 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:42:06,919 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=1523719f350c4dac83969b678e0b9163
2025-05-22 16:42:06,920 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=1523719f350c4dac83969b678e0b9163
2025-05-22 16:42:06,927 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=1523719f350c4dac83969b678e0b9163 "HTTP/1.1 202 Accepted"
2025-05-22 16:42:06,929 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 16:42:07,510 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:42:07,511 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=6ddab4491e1f494d93070fdf6682f832
2025-05-22 16:42:07,512 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=6ddab4491e1f494d93070fdf6682f832
2025-05-22 16:42:07,519 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6ddab4491e1f494d93070fdf6682f832 "HTTP/1.1 202 Accepted"
2025-05-22 16:42:07,546 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=1523719f350c4dac83969b678e0b9163 "HTTP/1.1 202 Accepted"
2025-05-22 16:42:07,567 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6ddab4491e1f494d93070fdf6682f832 "HTTP/1.1 202 Accepted"
2025-05-22 16:42:07,610 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=1523719f350c4dac83969b678e0b9163 "HTTP/1.1 202 Accepted"
2025-05-22 16:42:07,620 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=6ddab4491e1f494d93070fdf6682f832 "HTTP/1.1 202 Accepted"
2025-05-22 16:42:09,174 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:52:23,907 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-22 16:52:25,027 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-22 16:52:25,029 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-22 16:52:25,034 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:52:25,057 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:53:03,637 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:53:04,744 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:53:04,757 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 16:53:05,290 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:53:05,291 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=adfc96b1ffc543fb8952d6a2d4f75138
2025-05-22 16:53:05,291 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=adfc96b1ffc543fb8952d6a2d4f75138
2025-05-22 16:53:05,297 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=adfc96b1ffc543fb8952d6a2d4f75138 "HTTP/1.1 202 Accepted"
2025-05-22 16:53:05,298 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 16:53:05,846 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:53:05,847 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=23ae3b21ee494eb392eef6d80eeb5b48
2025-05-22 16:53:05,847 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=23ae3b21ee494eb392eef6d80eeb5b48
2025-05-22 16:53:05,854 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=23ae3b21ee494eb392eef6d80eeb5b48 "HTTP/1.1 202 Accepted"
2025-05-22 16:53:05,868 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=adfc96b1ffc543fb8952d6a2d4f75138 "HTTP/1.1 202 Accepted"
2025-05-22 16:53:05,910 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=23ae3b21ee494eb392eef6d80eeb5b48 "HTTP/1.1 202 Accepted"
2025-05-22 16:53:05,921 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=adfc96b1ffc543fb8952d6a2d4f75138 "HTTP/1.1 202 Accepted"
2025-05-22 16:53:05,964 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=23ae3b21ee494eb392eef6d80eeb5b48 "HTTP/1.1 202 Accepted"
2025-05-22 16:53:07,278 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:53:32,029 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:53:33,642 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:53:33,651 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 16:53:34,175 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:53:34,175 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=e019ca5529b540dcbc1d46c201008dc8
2025-05-22 16:53:34,176 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=e019ca5529b540dcbc1d46c201008dc8
2025-05-22 16:53:34,182 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e019ca5529b540dcbc1d46c201008dc8 "HTTP/1.1 202 Accepted"
2025-05-22 16:53:34,183 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 16:53:34,730 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:53:34,731 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=dfafa05d023e468a99c1592b3a59a654
2025-05-22 16:53:34,731 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=dfafa05d023e468a99c1592b3a59a654
2025-05-22 16:53:34,738 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=dfafa05d023e468a99c1592b3a59a654 "HTTP/1.1 202 Accepted"
2025-05-22 16:53:34,747 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e019ca5529b540dcbc1d46c201008dc8 "HTTP/1.1 202 Accepted"
2025-05-22 16:53:34,800 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=dfafa05d023e468a99c1592b3a59a654 "HTTP/1.1 202 Accepted"
2025-05-22 16:53:34,801 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=e019ca5529b540dcbc1d46c201008dc8 "HTTP/1.1 202 Accepted"
2025-05-22 16:53:34,854 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=dfafa05d023e468a99c1592b3a59a654 "HTTP/1.1 202 Accepted"
2025-05-22 16:53:35,959 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:53:46,866 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:53:48,093 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:53:48,101 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 16:53:48,659 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:53:48,660 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=04272948d20349c18cf2ef758940c0d0
2025-05-22 16:53:48,661 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=04272948d20349c18cf2ef758940c0d0
2025-05-22 16:53:48,667 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=04272948d20349c18cf2ef758940c0d0 "HTTP/1.1 202 Accepted"
2025-05-22 16:53:48,668 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 16:53:49,276 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:53:49,278 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=953afa55df3f4554bb3ec9e137d3a20b
2025-05-22 16:53:49,279 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=953afa55df3f4554bb3ec9e137d3a20b
2025-05-22 16:53:49,282 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=04272948d20349c18cf2ef758940c0d0 "HTTP/1.1 202 Accepted"
2025-05-22 16:53:49,307 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=953afa55df3f4554bb3ec9e137d3a20b "HTTP/1.1 202 Accepted"
2025-05-22 16:53:49,378 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=953afa55df3f4554bb3ec9e137d3a20b "HTTP/1.1 202 Accepted"
2025-05-22 16:53:49,380 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=04272948d20349c18cf2ef758940c0d0 "HTTP/1.1 202 Accepted"
2025-05-22 16:53:49,443 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=953afa55df3f4554bb3ec9e137d3a20b "HTTP/1.1 202 Accepted"
2025-05-22 16:53:52,096 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:53:53,305 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=04272948d20349c18cf2ef758940c0d0 "HTTP/1.1 202 Accepted"
2025-05-22 16:53:53,369 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=953afa55df3f4554bb3ec9e137d3a20b "HTTP/1.1 202 Accepted"
2025-05-22 16:53:53,422 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=953afa55df3f4554bb3ec9e137d3a20b "HTTP/1.1 202 Accepted"
2025-05-22 16:54:25,858 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:54:27,204 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:54:27,214 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 16:54:27,783 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:54:27,784 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=63a7e3616e8b48009fcb702377706fd1
2025-05-22 16:54:27,785 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=63a7e3616e8b48009fcb702377706fd1
2025-05-22 16:54:27,790 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=63a7e3616e8b48009fcb702377706fd1 "HTTP/1.1 202 Accepted"
2025-05-22 16:54:27,792 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 16:54:28,356 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:54:28,357 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=e8822cdc2f5f4ead86fc5d49a803f123
2025-05-22 16:54:28,357 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=e8822cdc2f5f4ead86fc5d49a803f123
2025-05-22 16:54:28,363 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e8822cdc2f5f4ead86fc5d49a803f123 "HTTP/1.1 202 Accepted"
2025-05-22 16:54:28,374 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=63a7e3616e8b48009fcb702377706fd1 "HTTP/1.1 202 Accepted"
2025-05-22 16:54:28,427 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e8822cdc2f5f4ead86fc5d49a803f123 "HTTP/1.1 202 Accepted"
2025-05-22 16:54:28,428 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=63a7e3616e8b48009fcb702377706fd1 "HTTP/1.1 202 Accepted"
2025-05-22 16:54:28,480 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=e8822cdc2f5f4ead86fc5d49a803f123 "HTTP/1.1 202 Accepted"
2025-05-22 16:54:29,527 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:54:57,932 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:54:59,301 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 16:54:59,311 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 16:54:59,962 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 16:54:59,963 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=5cd5661cfb4d4e5497da0b7b9a83dd1a
2025-05-22 16:54:59,963 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=5cd5661cfb4d4e5497da0b7b9a83dd1a
2025-05-22 16:54:59,969 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=5cd5661cfb4d4e5497da0b7b9a83dd1a "HTTP/1.1 202 Accepted"
2025-05-22 16:54:59,971 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 16:55:00,590 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 16:55:00,591 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=539449ac80ca4ea5ac4f86f95427f9ac
2025-05-22 16:55:00,591 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=539449ac80ca4ea5ac4f86f95427f9ac
2025-05-22 16:55:00,600 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=539449ac80ca4ea5ac4f86f95427f9ac "HTTP/1.1 202 Accepted"
2025-05-22 16:55:00,607 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=5cd5661cfb4d4e5497da0b7b9a83dd1a "HTTP/1.1 202 Accepted"
2025-05-22 16:55:00,650 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=539449ac80ca4ea5ac4f86f95427f9ac "HTTP/1.1 202 Accepted"
2025-05-22 16:55:00,671 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=5cd5661cfb4d4e5497da0b7b9a83dd1a "HTTP/1.1 202 Accepted"
2025-05-22 16:55:00,703 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=539449ac80ca4ea5ac4f86f95427f9ac "HTTP/1.1 202 Accepted"
2025-05-22 16:55:01,927 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 16:55:02,020 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=5cd5661cfb4d4e5497da0b7b9a83dd1a "HTTP/1.1 202 Accepted"
2025-05-22 16:55:02,072 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=539449ac80ca4ea5ac4f86f95427f9ac "HTTP/1.1 202 Accepted"
2025-05-22 16:55:02,139 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=539449ac80ca4ea5ac4f86f95427f9ac "HTTP/1.1 202 Accepted"
2025-05-22 16:55:04,779 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=539449ac80ca4ea5ac4f86f95427f9ac "HTTP/1.1 202 Accepted"
2025-05-22 16:55:04,780 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=5cd5661cfb4d4e5497da0b7b9a83dd1a "HTTP/1.1 202 Accepted"
2025-05-22 16:55:05,728 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 18:06:46,694 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 18:06:48,186 WARNING Overriding of current TracerProvider is not allowed
2025-05-22 18:06:48,202 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-22 18:06:48,942 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-22 18:06:48,943 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=d090ceb473de436da4dc4b2cbe110df1
2025-05-22 18:06:48,944 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=d090ceb473de436da4dc4b2cbe110df1
2025-05-22 18:06:48,959 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d090ceb473de436da4dc4b2cbe110df1 "HTTP/1.1 202 Accepted"
2025-05-22 18:06:48,968 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-22 18:06:49,860 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d090ceb473de436da4dc4b2cbe110df1 "HTTP/1.1 202 Accepted"
2025-05-22 18:06:49,987 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-22 18:06:49,988 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=467c8f74c133474bb85f91853c7d8ad5
2025-05-22 18:06:49,989 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=467c8f74c133474bb85f91853c7d8ad5
2025-05-22 18:06:50,003 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=467c8f74c133474bb85f91853c7d8ad5 "HTTP/1.1 202 Accepted"
2025-05-22 18:06:50,069 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=467c8f74c133474bb85f91853c7d8ad5 "HTTP/1.1 202 Accepted"
2025-05-22 18:06:50,070 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d090ceb473de436da4dc4b2cbe110df1 "HTTP/1.1 202 Accepted"
2025-05-22 18:06:50,125 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=467c8f74c133474bb85f91853c7d8ad5 "HTTP/1.1 202 Accepted"
2025-05-22 18:06:51,349 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-22 18:06:51,614 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d090ceb473de436da4dc4b2cbe110df1 "HTTP/1.1 202 Accepted"
2025-05-22 18:06:51,680 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=467c8f74c133474bb85f91853c7d8ad5 "HTTP/1.1 202 Accepted"
2025-05-22 18:06:51,736 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=467c8f74c133474bb85f91853c7d8ad5 "HTTP/1.1 202 Accepted"
2025-05-22 18:06:52,858 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=467c8f74c133474bb85f91853c7d8ad5 "HTTP/1.1 202 Accepted"
2025-05-22 18:06:52,859 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=d090ceb473de436da4dc4b2cbe110df1 "HTTP/1.1 202 Accepted"
2025-05-22 18:06:53,812 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-23 11:00:31,037 WARNING Overriding of current TracerProvider is not allowed
2025-05-23 11:00:32,339 WARNING Overriding of current TracerProvider is not allowed
2025-05-23 11:00:32,363 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-23 11:00:33,453 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 11:00:33,455 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=9ff99404bfcc41d08e5c752ac26d61cc
2025-05-23 11:00:33,456 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=9ff99404bfcc41d08e5c752ac26d61cc
2025-05-23 11:00:33,524 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9ff99404bfcc41d08e5c752ac26d61cc "HTTP/1.1 202 Accepted"
2025-05-23 11:00:33,556 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-23 11:00:34,335 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9ff99404bfcc41d08e5c752ac26d61cc "HTTP/1.1 202 Accepted"
2025-05-23 11:00:34,359 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 11:00:34,360 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=2e34f2ca2ef344d5b45e221774171376
2025-05-23 11:00:34,360 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=2e34f2ca2ef344d5b45e221774171376
2025-05-23 11:00:34,366 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2e34f2ca2ef344d5b45e221774171376 "HTTP/1.1 202 Accepted"
2025-05-23 11:00:34,424 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2e34f2ca2ef344d5b45e221774171376 "HTTP/1.1 202 Accepted"
2025-05-23 11:00:34,426 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9ff99404bfcc41d08e5c752ac26d61cc "HTTP/1.1 202 Accepted"
2025-05-23 11:00:34,474 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2e34f2ca2ef344d5b45e221774171376 "HTTP/1.1 202 Accepted"
2025-05-23 11:00:36,250 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-23 11:00:39,733 INFO HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:batchEmbedContents "HTTP/1.1 200 OK"
2025-05-23 11:00:43,203 INFO HTTP Request: POST https://gttwzegcmextyljehehj.supabase.co/rest/v1/rpc/match_documents_768 "HTTP/2 200 OK"
2025-05-23 11:00:43,403 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=2e34f2ca2ef344d5b45e221774171376 "HTTP/1.1 202 Accepted"
2025-05-23 11:00:43,404 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=9ff99404bfcc41d08e5c752ac26d61cc "HTTP/1.1 202 Accepted"
2025-05-23 11:00:44,824 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-23 11:01:13,484 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 11:01:14,961 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 11:01:14,964 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 11:01:14,970 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 11:01:15,058 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 11:01:18,514 WARNING Overriding of current TracerProvider is not allowed
2025-05-23 11:01:19,793 WARNING Overriding of current TracerProvider is not allowed
2025-05-23 11:01:19,806 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-23 11:01:20,394 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 11:01:20,395 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=6e547fde5be14a03a8f7950a05ba7f05
2025-05-23 11:01:20,395 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=6e547fde5be14a03a8f7950a05ba7f05
2025-05-23 11:01:20,401 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=6e547fde5be14a03a8f7950a05ba7f05 "HTTP/1.1 202 Accepted"
2025-05-23 11:01:20,402 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-23 11:01:21,610 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=6e547fde5be14a03a8f7950a05ba7f05 "HTTP/1.1 202 Accepted"
2025-05-23 11:01:21,639 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 11:01:21,640 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=adc4c3165d8f462991660664899cce44
2025-05-23 11:01:21,640 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=adc4c3165d8f462991660664899cce44
2025-05-23 11:01:21,656 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=adc4c3165d8f462991660664899cce44 "HTTP/1.1 202 Accepted"
2025-05-23 11:01:21,711 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=adc4c3165d8f462991660664899cce44 "HTTP/1.1 202 Accepted"
2025-05-23 11:01:21,712 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=6e547fde5be14a03a8f7950a05ba7f05 "HTTP/1.1 202 Accepted"
2025-05-23 11:01:21,761 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=adc4c3165d8f462991660664899cce44 "HTTP/1.1 202 Accepted"
2025-05-23 11:01:23,209 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-23 11:02:13,487 WARNING Overriding of current TracerProvider is not allowed
2025-05-23 11:02:14,994 WARNING Overriding of current TracerProvider is not allowed
2025-05-23 11:02:15,005 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-23 11:02:15,633 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 11:02:15,633 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=03f4cdba7dd5495b9609847e96c0b660
2025-05-23 11:02:15,634 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=03f4cdba7dd5495b9609847e96c0b660
2025-05-23 11:02:15,642 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=03f4cdba7dd5495b9609847e96c0b660 "HTTP/1.1 202 Accepted"
2025-05-23 11:02:15,644 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-23 11:02:16,306 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 11:02:16,307 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=ec44c69c9f304feeae792b1e4d758ec2
2025-05-23 11:02:16,307 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=ec44c69c9f304feeae792b1e4d758ec2
2025-05-23 11:02:16,313 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=03f4cdba7dd5495b9609847e96c0b660 "HTTP/1.1 202 Accepted"
2025-05-23 11:02:16,314 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=ec44c69c9f304feeae792b1e4d758ec2 "HTTP/1.1 202 Accepted"
2025-05-23 11:02:16,373 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=ec44c69c9f304feeae792b1e4d758ec2 "HTTP/1.1 202 Accepted"
2025-05-23 11:02:16,374 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=03f4cdba7dd5495b9609847e96c0b660 "HTTP/1.1 202 Accepted"
2025-05-23 11:02:16,424 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=ec44c69c9f304feeae792b1e4d758ec2 "HTTP/1.1 202 Accepted"
2025-05-23 11:02:17,776 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-23 11:55:35,503 WARNING Overriding of current TracerProvider is not allowed
2025-05-23 11:55:37,548 WARNING Overriding of current TracerProvider is not allowed
2025-05-23 11:55:37,558 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-23 11:55:38,165 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 11:55:38,166 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=b1da5eea181b4f0d8f4211b724ce209a
2025-05-23 11:55:38,166 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=b1da5eea181b4f0d8f4211b724ce209a
2025-05-23 11:55:38,172 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b1da5eea181b4f0d8f4211b724ce209a "HTTP/1.1 202 Accepted"
2025-05-23 11:55:38,173 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-23 11:55:38,845 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 11:55:38,845 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=b287c4a57c4244dc8d84026caa5ba4fe
2025-05-23 11:55:38,846 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=b287c4a57c4244dc8d84026caa5ba4fe
2025-05-23 11:55:38,867 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b1da5eea181b4f0d8f4211b724ce209a "HTTP/1.1 202 Accepted"
2025-05-23 11:55:38,870 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=b287c4a57c4244dc8d84026caa5ba4fe "HTTP/1.1 202 Accepted"
2025-05-23 11:55:38,937 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=b287c4a57c4244dc8d84026caa5ba4fe "HTTP/1.1 202 Accepted"
2025-05-23 11:55:38,938 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=b1da5eea181b4f0d8f4211b724ce209a "HTTP/1.1 202 Accepted"
2025-05-23 11:55:38,988 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=b287c4a57c4244dc8d84026caa5ba4fe "HTTP/1.1 202 Accepted"
2025-05-23 11:55:40,510 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-23 12:36:45,568 WARNING Overriding of current TracerProvider is not allowed
2025-05-23 12:36:55,395 INFO Creating new collection: mem0_memories768
2025-05-23 12:36:56,488 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-23 12:36:58,994 INFO Creating new collection: mem0migrations
2025-05-23 12:36:59,762 INFO Successfully created collection mem0migrations with dimension 768
2025-05-23 12:37:00,008 INFO Inserting 1 vectors into collection mem0migrations
2025-05-23 12:37:00,317 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-23 12:37:00,318 INFO Retrieving memories for user_id=user-kn6zpkhs, query='hi'
2025-05-23 12:37:03,060 INFO Retrieved memory for user_id=user-kn6zpkhs, query='hi': {'results': []}
2025-05-23 12:37:03,160 WARNING Overriding of current TracerProvider is not allowed
2025-05-23 12:37:03,183 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-23 12:37:03,944 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 12:37:03,945 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=59d04a34db4c48ebb7a88b9f732a3fd9
2025-05-23 12:37:03,946 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=59d04a34db4c48ebb7a88b9f732a3fd9
2025-05-23 12:37:03,957 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=59d04a34db4c48ebb7a88b9f732a3fd9 "HTTP/1.1 202 Accepted"
2025-05-23 12:37:03,966 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-23 12:37:04,658 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=59d04a34db4c48ebb7a88b9f732a3fd9 "HTTP/1.1 202 Accepted"
2025-05-23 12:37:04,691 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 12:37:04,692 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=48e4373c6df14628b934fbd0be46ff2c
2025-05-23 12:37:04,692 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=48e4373c6df14628b934fbd0be46ff2c
2025-05-23 12:37:04,702 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=48e4373c6df14628b934fbd0be46ff2c "HTTP/1.1 202 Accepted"
2025-05-23 12:37:04,755 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=59d04a34db4c48ebb7a88b9f732a3fd9 "HTTP/1.1 202 Accepted"
2025-05-23 12:37:04,755 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=48e4373c6df14628b934fbd0be46ff2c "HTTP/1.1 202 Accepted"
2025-05-23 12:37:04,804 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=48e4373c6df14628b934fbd0be46ff2c "HTTP/1.1 202 Accepted"
2025-05-23 12:37:05,374 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-23 12:37:05,520 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=59d04a34db4c48ebb7a88b9f732a3fd9 "HTTP/1.1 202 Accepted"
2025-05-23 12:37:05,580 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=59d04a34db4c48ebb7a88b9f732a3fd9 "HTTP/1.1 202 Accepted"
2025-05-23 12:37:05,784 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=48e4373c6df14628b934fbd0be46ff2c "HTTP/1.1 202 Accepted"
2025-05-23 12:37:05,787 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=59d04a34db4c48ebb7a88b9f732a3fd9 "HTTP/1.1 202 Accepted"
2025-05-23 12:37:06,265 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-23 12:37:06,356 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=59d04a34db4c48ebb7a88b9f732a3fd9 "HTTP/1.1 202 Accepted"
2025-05-23 12:37:06,418 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=48e4373c6df14628b934fbd0be46ff2c "HTTP/1.1 202 Accepted"
2025-05-23 12:37:06,484 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=48e4373c6df14628b934fbd0be46ff2c "HTTP/1.1 202 Accepted"
2025-05-23 12:37:06,681 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=48e4373c6df14628b934fbd0be46ff2c "HTTP/1.1 202 Accepted"
2025-05-23 12:37:06,691 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=59d04a34db4c48ebb7a88b9f732a3fd9 "HTTP/1.1 202 Accepted"
2025-05-23 12:37:07,121 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-23 12:37:09,024 INFO Creating new collection: mem0_memories768
2025-05-23 12:37:09,786 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-23 12:37:13,095 INFO Creating new collection: mem0migrations
2025-05-23 12:37:13,820 INFO Successfully created collection mem0migrations with dimension 768
2025-05-23 12:37:14,162 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-23 12:37:14,162 INFO Adding memory for user_id=user-kn6zpkhs
2025-05-23 12:37:15,267 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-23 12:37:15,449 INFO Total existing memories: 0
2025-05-23 12:37:16,037 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-23 12:37:27,517 WARNING Overriding of current TracerProvider is not allowed
2025-05-23 12:37:30,450 INFO Creating new collection: mem0_memories768
2025-05-23 12:37:31,195 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-23 12:37:33,801 INFO Creating new collection: mem0migrations
2025-05-23 12:37:34,478 INFO Successfully created collection mem0migrations with dimension 768
2025-05-23 12:37:34,745 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-23 12:37:34,746 INFO Retrieving memories for user_id=user-kn6zpkhs, query='What are your capablities'
2025-05-23 12:37:36,110 INFO Retrieved memory for user_id=user-kn6zpkhs, query='What are your capablities': {'results': []}
2025-05-23 12:37:36,660 WARNING Overriding of current TracerProvider is not allowed
2025-05-23 12:37:36,746 INFO Connecting to SSE endpoint: http://localhost:4747/sse
2025-05-23 12:37:37,988 INFO HTTP Request: GET http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 12:37:37,989 INFO Received endpoint URL: http://localhost:4747/messages/?session_id=de56b70f7c764c278cb5210111874c4f
2025-05-23 12:37:37,990 INFO Starting post writer with endpoint URL: http://localhost:4747/messages/?session_id=de56b70f7c764c278cb5210111874c4f
2025-05-23 12:37:37,999 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=de56b70f7c764c278cb5210111874c4f "HTTP/1.1 202 Accepted"
2025-05-23 12:37:38,001 INFO Connecting to SSE endpoint: http://localhost:4749/sse
2025-05-23 12:37:39,339 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=de56b70f7c764c278cb5210111874c4f "HTTP/1.1 202 Accepted"
2025-05-23 12:37:39,343 INFO HTTP Request: GET http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 12:37:39,344 INFO Received endpoint URL: http://localhost:4749/messages/?session_id=23e2a582d4504095b9322639598190ac
2025-05-23 12:37:39,345 INFO Starting post writer with endpoint URL: http://localhost:4749/messages/?session_id=23e2a582d4504095b9322639598190ac
2025-05-23 12:37:39,368 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=23e2a582d4504095b9322639598190ac "HTTP/1.1 202 Accepted"
2025-05-23 12:37:39,420 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=23e2a582d4504095b9322639598190ac "HTTP/1.1 202 Accepted"
2025-05-23 12:37:39,424 INFO HTTP Request: POST http://localhost:4747/messages/?session_id=de56b70f7c764c278cb5210111874c4f "HTTP/1.1 202 Accepted"
2025-05-23 12:37:39,481 INFO HTTP Request: POST http://localhost:4749/messages/?session_id=23e2a582d4504095b9322639598190ac "HTTP/1.1 202 Accepted"
2025-05-23 12:37:39,953 INFO HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-23 12:37:42,549 INFO Creating new collection: mem0_memories768
2025-05-23 12:37:43,270 INFO Successfully created collection mem0_memories768 with dimension 768
2025-05-23 12:37:45,729 INFO Creating new collection: mem0migrations
2025-05-23 12:37:46,442 INFO Successfully created collection mem0migrations with dimension 768
2025-05-23 12:37:46,715 INFO Initialized Mem0Manager with Gemini 768 config.
2025-05-23 12:37:46,716 INFO Adding memory for user_id=user-kn6zpkhs
2025-05-23 12:37:48,153 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-23 12:37:48,429 INFO Total existing memories: 0
2025-05-23 12:37:49,330 INFO HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-23 13:20:33,744 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 13:20:34,849 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 13:20:34,852 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 13:20:34,874 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 13:20:34,910 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 13:20:48,129 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 13:20:49,237 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 13:20:49,238 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 13:20:49,244 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 13:20:49,264 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:17:12,001 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:17:13,285 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:17:13,286 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:17:13,299 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:17:13,319 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:17:55,169 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:17:56,734 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:17:56,736 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:17:56,743 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:17:56,772 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:22:01,044 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:22:02,303 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:22:02,304 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:22:02,310 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:22:02,340 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:23:03,946 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:23:05,259 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:23:05,261 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:23:05,268 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:23:05,312 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:23:45,001 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:23:46,463 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:23:46,465 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:23:46,470 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:23:46,499 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:24:37,613 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:24:38,816 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:24:38,817 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:24:38,822 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:24:38,842 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:24:59,981 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:25:01,329 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:25:01,330 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:25:01,336 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:25:01,361 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:25:24,217 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:25:28,360 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:25:28,364 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:25:28,372 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:25:28,456 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:26:58,572 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:26:59,940 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:26:59,941 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:26:59,947 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:26:59,973 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:27:12,469 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:27:13,655 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:27:13,656 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:27:13,660 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:27:13,680 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:30:25,862 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:30:28,050 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:30:28,052 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:30:28,060 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:30:28,100 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:32:39,931 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:32:39,934 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:32:39,938 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:32:39,961 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:32:56,177 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:32:56,180 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:32:56,189 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:32:56,217 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:34:46,612 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:34:48,130 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:34:48,131 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:34:48,137 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:34:48,165 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:37:18,810 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:37:20,148 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:37:20,150 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:37:20,157 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:37:20,181 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:38:10,608 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:38:11,977 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:38:11,979 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:38:11,984 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:38:12,010 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:49:04,136 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:49:05,369 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:49:05,370 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:49:05,375 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:49:05,403 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:51:00,682 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:51:01,897 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:51:01,899 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:51:01,905 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:51:01,935 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:51:21,703 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:51:22,747 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:51:22,748 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:51:22,752 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:51:22,771 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:51:35,910 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:51:37,207 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:51:37,208 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:51:37,212 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:51:37,232 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:55:36,067 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:55:37,414 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:55:37,444 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:55:37,445 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:55:37,452 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:57:44,362 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:57:45,485 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:57:45,486 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:57:45,490 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:57:45,512 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 14:58:23,195 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 14:58:24,381 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 14:58:24,382 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 14:58:24,386 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 14:58:24,417 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:00:10,565 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:00:12,360 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:00:12,364 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:00:12,366 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:00:12,403 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:03:44,884 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:03:45,900 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:03:45,901 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:03:45,905 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:03:45,925 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:04:02,529 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:04:04,070 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:04:04,072 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:04:04,078 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:04:04,102 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:10:25,725 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:10:26,740 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:10:26,741 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:10:26,746 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:10:26,765 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:10:40,646 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:10:41,821 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:10:41,822 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:10:41,827 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:10:41,850 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:13:12,612 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:13:13,945 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:13:13,946 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:13:13,953 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:13:13,980 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:13:27,664 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:13:29,288 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:13:29,289 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:13:29,296 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:13:29,326 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:14:33,932 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:14:38,671 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:14:38,672 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:14:38,677 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:14:38,703 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:19:56,340 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:19:57,477 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:19:57,478 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:19:57,484 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:19:57,505 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:20:11,599 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:20:12,817 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:20:12,818 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:20:12,823 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:20:12,846 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:20:42,876 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:20:44,461 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:20:44,462 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:20:44,467 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:20:44,489 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:21:28,577 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:21:30,245 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:21:30,246 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:21:30,252 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:21:30,276 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:21:44,013 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:21:45,181 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:21:45,183 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:21:45,188 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:21:45,210 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:22:19,018 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:22:20,418 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:22:20,419 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:22:20,424 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:22:20,446 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:22:42,488 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:22:43,769 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:22:43,770 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:22:43,775 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:22:43,800 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:23:17,075 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:23:18,314 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:23:18,315 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:23:18,321 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:23:18,355 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:23:49,342 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:23:50,547 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:23:50,549 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:23:50,554 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:23:50,580 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:24:12,648 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:24:13,864 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:24:13,865 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:24:13,870 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:24:13,891 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-23 15:24:26,922 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-23 15:24:28,160 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-23 15:24:28,161 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-23 15:24:28,165 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-23 15:24:28,187 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 10:43:11,963 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 10:43:13,189 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 10:43:13,193 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 10:43:13,228 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 10:43:13,476 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 10:54:47,811 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 10:54:52,369 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 10:54:52,370 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 10:54:52,380 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 10:54:52,419 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 12:28:47,196 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 12:28:48,374 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 12:28:48,375 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 12:28:48,399 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 12:28:48,442 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 13:09:26,146 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 13:09:27,479 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 13:09:27,481 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 13:09:27,489 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 13:09:27,523 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 13:14:41,818 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 13:14:43,040 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 13:14:43,041 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 13:14:43,046 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 13:14:43,066 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 13:20:30,946 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 13:20:32,079 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 13:20:32,081 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 13:20:32,090 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 13:20:32,127 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 13:20:43,962 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 13:20:45,086 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 13:20:45,086 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 13:20:45,091 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 13:20:45,112 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 13:22:23,977 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 13:22:25,043 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 13:22:25,044 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 13:22:25,049 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 13:22:25,067 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 13:23:20,094 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 13:23:21,129 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 13:23:21,130 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 13:23:21,134 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 13:23:21,152 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 13:23:34,102 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 13:23:35,110 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 13:23:35,111 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 13:23:35,116 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 13:23:35,135 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 13:23:46,841 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 13:23:47,921 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 13:23:47,923 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 13:23:47,928 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 13:23:47,950 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 13:24:19,143 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 13:24:20,306 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 13:24:20,307 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 13:24:20,311 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 13:24:20,336 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 13:27:45,879 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 13:27:46,937 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 13:27:46,938 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 13:27:46,943 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 13:27:46,964 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 13:31:12,905 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 13:31:14,074 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 13:31:14,075 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 13:31:14,079 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 13:31:14,100 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 13:34:15,766 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 13:34:16,761 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 13:34:16,762 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 13:34:16,766 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 13:34:16,784 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 13:34:28,431 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 13:34:29,596 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 13:34:29,597 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 13:34:29,602 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 13:34:29,627 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 13:35:37,036 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 13:35:38,134 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 13:35:38,135 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 13:35:38,139 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 13:35:38,159 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 13:48:40,960 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 13:48:42,841 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 13:48:42,842 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 13:48:42,850 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 13:48:42,881 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
2025-05-24 13:49:03,888 INFO HTTP Request: GET https://gttwzegcmextyljehehj.supabase.co/rest/v1/document_rows?select=row_data&limit=1 "HTTP/2 200 OK"
2025-05-24 13:49:05,303 INFO HTTP Request: GET http://localhost:4747/health "HTTP/1.1 200 OK"
2025-05-24 13:49:05,307 INFO HTTP Request: HEAD http://localhost:4747/sse "HTTP/1.1 200 OK"
2025-05-24 13:49:05,317 INFO HTTP Request: GET http://localhost:4749/health "HTTP/1.1 200 OK"
2025-05-24 13:49:05,341 INFO HTTP Request: HEAD http://localhost:4749/sse "HTTP/1.1 200 OK"
