# Plan: Update Prompt Loader for <PERSON><PERSON> Agent (12 May 2025, 13:39)

## Objective
- Update `src/prompts/loader.py` to better align with the agent's role, flow, and requirements as described in `system_prompt.md`.
- Ensure prompt variables and formatting support all required agent behaviors and response formats.

## Steps
1. Review `system_prompt.md` for all agent requirements, flow, and formatting rules.
2. Update or add prompt variables to support agent name, organization, and any new context needed.
3. Add functions to load and format prompts for each step in the agent flow (telecaller, RAG, rule engine, etc.).
4. Add helpers for markdown formatting (tables, images, links) as per response format rules.
5. Update docstrings and type hints per best practices.
6. Run ruff linting and fix issues.
7. Update or add tests if needed.
8. Document public API and usage. 