// Provider and model configuration for use across the app
// Generated from services/providers.json

export const PROVIDERS = {
  openrouter: {
    base_url: "https://openrouter.ai/api/v1",
    description: "OpenRouter provides access to various LLM models through a unified API",
    provider_class: "OpenAIProvider",
    model_class: "OpenAIModel",
    models: {
      "anthropic/claude-3.5-sonnet": {
        MAX_COMPLETION_TOKENS: 8192,
        CONTEXT_WINDOW: 200000,
        description: "Claude 3.5 Sonnet - Anthropic's powerful and efficient model"
      },
      "anthropic/claude-3.7-sonnet": {
        MAX_COMPLETION_TOKENS: 64000,
        CONTEXT_WINDOW: 200000,
        description: "Claude 3.7 Sonnet - Anthrop<PERSON>'s latest and most capable model"
      },
      "meta-llama/llama-3.1-8b-instruct": {
        MAX_COMPLETION_TOKENS: 4096,
        CONTEXT_WINDOW: 8192,
        description: "Llama 3.1 8B - Met<PERSON>'s compact instruction-tuned model"
      }
    }
  },
  groq: {
    base_url: "https://api.groq.com",
    description: "Groq provides high-performance inference for various LLM models",
    provider_class: "GroqProvider",
    model_class: "GroqModel",
    models: {
      "llama-3.3-70b-versatile": {
        MAX_COMPLETION_TOKENS: 8192,
        CONTEXT_WINDOW: 128000,
        description: "Llama 3.3 70B - Meta's versatile large language model"
      },
      "meta-llama/llama-4-maverick-17b-128e-instruct": {
        MAX_COMPLETION_TOKENS: 8192,
        CONTEXT_WINDOW: 131072,
        description: "Llama 4 Maverick 17B - Meta's latest instruction-tuned model"
      },
      "deepseek-r1-distill-llama-70b": {
        MAX_COMPLETION_TOKENS: 4096,
        CONTEXT_WINDOW: 128000,
        description: "DeepSeek R1 Distill Llama 70B - DeepSeek's compact instruction-tuned model"
      }
    }
  }
} as const;

// Allowed domains for authentication
export const ALLOWED_DOMAINS = [
  { value: "olaelectric.com", label: "@olaelectric.com" },
  { value: "olacabs.com", label: "@olacabs.com" },
  { value: "olakrutrim.com", label: "@olakrutrim.com" }
]; 