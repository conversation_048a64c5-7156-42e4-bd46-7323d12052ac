# Plan: Move High-Level Langfuse Tracing to core.py (12 May 25 18:27)

## Rationale
- Tracing at the API layer (core.py) allows capturing the full lifecycle of a chat session, including MCP context, user/session info, and agent input/output.
- This provides better observability and separation of concerns compared to tracing only within agent.py.
- Tool-level tracing can remain in agent.py for fine-grained detail.

## Steps
1. Remove high-level agent input/output tracing (run, run_stream) from agent.py.
2. In core.py, at the /chat/{session_id}/ endpoint:
    - Initialize LangfuseIntegration and get a tracer.
    - Start a Langfuse span at the beginning of the endpoint or stream_messages function.
    - **Set recommended Langfuse attributes on the span:**
        - `langfuse.user.id` (user_id)
        - `langfuse.session.id` (session_id)
        - `langfuse.tags` (e.g., ["chat", "mcp"])
        - `input.prompt` (user_query)
        - Any other relevant metadata (model, etc.)
    - Enter the MCP context manager and agent.run_stream context manager inside the span.
    - **After the agent call, set output attributes on the span:**
        - `output.llm_response` (LLM response text)
    - Yield/return the response as usual.
3. Ensure tool-level tracing remains in agent.py for retrieve_documents, retrieve_feedback, etc.
4. Test that the full session, including MCP context, is traced in Langfuse, and that recommended attributes are visible in the trace.
5. Update documentation/readme as needed. 