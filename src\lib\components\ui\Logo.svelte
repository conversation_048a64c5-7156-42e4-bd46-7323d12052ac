<script lang="ts">
	import { cn } from '$lib/utils';

	let {
		className,
		size = 'default',
		variant = 'dark'
	}: {
		className?: string;
		size?: 'small' | 'default' | 'large';
		variant?: 'dark' | 'light';
	} = $props();

	const sizeClasses: Record<'small' | 'default' | 'large', string> = {
		small: 'h-20',
		default: 'h-32',
		large: 'h-48'
	};

	const logoSrc = variant === 'dark' ? 'img/vs_logo_dark.png' : 'img/vs_logo.png';
</script>

<div
	class={cn(
		'flex flex-col items-center justify-center',
		className
	)}
>
	<img
		src={logoSrc}
		alt="Vahan Sahayak Advanced Logo {variant === 'dark' ? 'Dark' : 'Light'}"
		class={cn(sizeClasses[size])}
	/>
</div>
