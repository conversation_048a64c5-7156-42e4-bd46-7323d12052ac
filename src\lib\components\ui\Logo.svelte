<script lang="ts">
	import { onMount } from 'svelte';
	import { tweened } from 'svelte/motion';
	import { cubicInOut } from 'svelte/easing';
	import { cn } from '$lib/utils';

	let {
		className,
		size = 'default',
		variant = 'dark'
	}: {
		className?: string;
		size?: 'small' | 'default' | 'large';
		variant?: 'dark' | 'light';
	} = $props();

	const sizeClasses: Record<'small' | 'default' | 'large', string> = {
		small: 'h-20',
		default: 'h-32',
		large: 'h-48'
	};

	const logoSrc = variant === 'dark' ? 'img/vs_logo_dark.png' : 'img/vs_logo.png';

	// Motion Instance 1 (Right Conic)
	let motion1Ref = $state<HTMLDivElement | null>(null);
	const opacity1 = tweened(0.4, { duration: 800, delay: 300, easing: cubicInOut });
	const width1 = tweened(10, { duration: 800, delay: 300, easing: cubicInOut }); // in rem

	// Motion Instance 2 (Left Conic)
	let motion2Ref = $state<HTMLDivElement | null>(null);
	const opacity2 = tweened(0.4, { duration: 800, delay: 300, easing: cubicInOut });
	const width2 = tweened(10, { duration: 800, delay: 300, easing: cubicInOut }); // in rem

	// Motion Instance 3 (Blurry Oval)
	let motion3Ref = $state<HTMLDivElement | null>(null);
	const width3 = tweened(6, { duration: 800, delay: 300, easing: cubicInOut }); // in rem

	// Motion Instance 4 (Horizontal Line)
	let motion4Ref = $state<HTMLDivElement | null>(null);
	const width4 = tweened(10, { duration: 800, delay: 300, easing: cubicInOut }); // in rem

	onMount(() => {
		const observerOptions = {
			root: null,
			rootMargin: '0px',
			threshold: 0.1 // Trigger when 10% of the element is visible
		};

		const observerCallback = (entries: IntersectionObserverEntry[]) => {
			entries.forEach((entry) => {
				if (entry.isIntersecting) {
					if (entry.target === motion1Ref) {
						opacity1.set(0.8);
						width1.set(20);
					} else if (entry.target === motion2Ref) {
						opacity2.set(0.8);
						width2.set(20);
					} else if (entry.target === motion3Ref) {
						width3.set(12);
					} else if (entry.target === motion4Ref) {
						width4.set(20);
					}
				}
			});
		};

		const observer = new IntersectionObserver(observerCallback, observerOptions);

		if (motion1Ref) observer.observe(motion1Ref);
		if (motion2Ref) observer.observe(motion2Ref);
		if (motion3Ref) observer.observe(motion3Ref);
		if (motion4Ref) observer.observe(motion4Ref);

		return () => {
			observer.disconnect();
		};
	});
</script>

<div
	class={cn(
		'relative flex w-full flex-col items-center justify-center overflow-visible',
		className
	)}
>
	<!-- Lamp effects container - behind everything -->
	<div class="absolute inset-0 -z-10 flex w-full scale-75 items-center justify-center overflow-visible">

		<div
			bind:this={motion1Ref}
			style="opacity: {$opacity1}; width: {$width1}rem; background-image: conic-gradient(var(--conic-position), var(--tw-gradient-stops));"
			class="bg-gradient-conic absolute inset-auto right-1/2 h-40 -z-20 overflow-visible from-cyan-500 via-transparent to-transparent text-white [--conic-position:from_70deg_at_center_top]"
		>
		</div>

		<div
			bind:this={motion2Ref}
			style="opacity: {$opacity2}; width: {$width2}rem; background-image: conic-gradient(var(--conic-position), var(--tw-gradient-stops));"
			class="bg-gradient-conic absolute inset-auto left-1/2 h-40 -z-20 overflow-visible from-transparent via-transparent to-cyan-500 text-white [--conic-position:from_290deg_at_center_top]"
		>
		</div>

		<div
			class="absolute inset-auto -z-30 h-48 w-[40rem] -translate-y-1/2 rounded-full bg-cyan-500 opacity-40 blur-[100px]"
		></div>

		<div
			bind:this={motion3Ref}
			style="width: {$width3}rem;"
			class="absolute inset-auto -z-25 h-32 -translate-y-[5rem] rounded-full bg-cyan-400 blur-[60px]"
		></div>

		<div
			bind:this={motion4Ref}
			style="width: {$width4}rem;"
			class="absolute inset-auto -z-20 h-0.5 -translate-y-[6rem] bg-cyan-400"
		></div>
	</div>

	<!-- Logo image - above the lamp effects with positive z-index -->
	<div class="relative z-10 flex flex-col items-center justify-center px-5 py-8">
		<img
			src={logoSrc}
			alt="Vahan Sahayak Advanced Logo {variant === 'dark' ? 'Dark' : 'Light'}"
			class={cn(sizeClasses[size], 'relative z-20')}
		/>
	</div>
</div>
