# Tasks: Langfuse Tracing Integration (12 May 25 18:08)

- [x] **Add <PERSON>fuse tracing to tool calls in AgentModule**
    - Wrap `retrieve_documents` and `retrieve_feedback` with Langfuse tracing if enabled.
    - Ensure tracing decorator is applied dynamically in the constructor.

- [x] **Add Langfuse tracing to agent input/output**
    - Wrap the main agent run methods (`run`, `run_stream`) with Langfuse tracing if enabled.
    - Ensure the user prompt and LLM response are captured in Langfuse traces.

- [ ] **Test and validate integration**
    - Confirm that tool calls and agent input/output are traced in Langfuse when enabled.
    - Confirm that the agent works as expected with Langfuse tracing.
    - Update documentation/readme if needed. 