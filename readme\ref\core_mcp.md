This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

# File Summary

## Purpose
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

## File Format
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Multiple file entries, each consisting of:
  a. A header with the file path (## File: path/to/file)
  b. The full contents of the file in a code block

## Usage Guidelines
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

## Notes
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: src/**/core.py, src/**/integrations/telematics.py
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)

## Additional Info

# Directory Structure
```
src/vahan_sahayak/core.py
src/vahan_sahayak/db_manager/core.py
src/vahan_sahayak/integrations/telematics.py
```

# Files

## File: src/vahan_sahayak/db_manager/core.py
```python
"""
Core logic for managing chat history in the database.
"""
import uuid
import json
from typing import List, Optional, Tuple
from sqlalchemy.orm import Session
from ..models import StandardMessage  # Corrected import path
from .models import ChatSession
from .utils import SessionLocal, get_engine, init_db # init_db might be called elsewhere

class ChatHistoryDBManager:
    """
    Manages storing and retrieving chat history from an SQLite database.
    """

    def __init__(self, session_local=None):
        """
        Initializes the ChatHistoryDBManager.
        Args:
            session_local: SQLAlchemy sessionmaker. If None, uses SessionLocal from utils.
        """
        self.SessionLocal = session_local or SessionLocal
        # Ensure the database and tables are created if they don't exist.
        # This is a good place to call init_db() if it's not guaranteed to be called
        # elsewhere before the manager is used for the first time.
        # However, typically init_db() is called once at application startup.
        # For robustness in a standalone module, we can call it here,
        # but it's idempotent due to `create_all(checkfirst=True)` implicitly.
        # Let's assume init_db() is called at app startup.
        # If not, uncomment:
        # init_db()


    def _get_db(self) -> Session:
        """Provides a database session."""
        return self.SessionLocal()

    def save_history(self, user_id: str, session_id: str, history: List[StandardMessage]) -> None:
        """
        Saves or updates the chat history for a given user and session.

        Args:
            user_id: The ID of the user.
            session_id: The ID of the session.
            history: A list of StandardMessage objects representing the chat history.
        """
        db = self._get_db()
        try:
            session_record = db.query(ChatSession).filter(ChatSession.session_id == session_id).first()
            
            # Convert StandardMessage objects to a list of dictionaries
            history_json_serializable = [msg.model_dump() for msg in history]

            if session_record:
                session_record.history = history_json_serializable
                # updated_at is handled by SQLAlchemy's onupdate
                # session_record.updated_at = datetime.datetime.utcnow() # Not needed if onupdate is set
            else:
                session_record = ChatSession(
                    user_id=user_id,
                    session_id=session_id,
                    history=history_json_serializable
                    # created_at and updated_at have defaults
                )
                db.add(session_record)
            
            db.commit()
            db.refresh(session_record) # To get updated timestamps etc.
        except Exception as e:
            db.rollback()
            # Log error e
            raise e
        finally:
            db.close()

    def get_history(self, session_id: str) -> Optional[List[StandardMessage]]:
        """
        Retrieves the chat history for a given session_id.

        Args:
            session_id: The ID of the session.

        Returns:
            A list of StandardMessage objects if the session is found, otherwise None.
        """
        db = self._get_db()
        try:
            session_record = db.query(ChatSession).filter(ChatSession.session_id == session_id).first()
            if session_record:
                # The history is stored as a list of dicts (JSON array)
                history_data = session_record.history 
                if isinstance(history_data, str): # If DB stores JSON as string
                    history_data = json.loads(history_data)
                
                # Convert list of dictionaries back to StandardMessage objects
                return [StandardMessage(**msg_data) for msg_data in history_data]
            return None
        finally:
            db.close()

    def get_or_create_session(self, user_id: str, session_id: Optional[str] = None) -> Tuple[str, List[StandardMessage]]:
        """
        Retrieves an existing session and its history for the given user_id.
        If session_id is provided and exists for the user, its history is returned.
        If session_id is provided but does not exist for the user, a new session is created
        using the provided session_id.
        If session_id is not provided, a new session_id is generated and a new session is created.

        Args:
            user_id: The ID of the user.
            session_id: Optional. The ID of the session.

        Returns:
            A tuple containing the session_id (str) and its history (List[StandardMessage]).
        """
        db = self._get_db()
        try:
            history_to_return = []
            session_id_to_use = session_id

            if session_id_to_use:
                # Attempt to retrieve existing session for this user
                session_record = db.query(ChatSession).filter(
                    ChatSession.session_id == session_id_to_use,
                    ChatSession.user_id == user_id
                ).first()

                if session_record:
                    # Session found for this user, parse and return history
                    history_data = session_record.history
                    if isinstance(history_data, str): # If DB stores JSON as string
                        history_data = json.loads(history_data)
                    
                    # Convert list of dictionaries back to StandardMessage objects
                    history_to_return = [StandardMessage(**msg_data) for msg_data in history_data]
                    return session_id_to_use, history_to_return
                # else: session_id provided, but not found for this user.
                # We will create it using the provided session_id_to_use.
                # history_to_return remains []
            else:
                # No session_id provided by client, generate a new one.
                session_id_to_use = str(uuid.uuid4())
                # history_to_return remains []

            # Create new session record if it wasn't found or if no ID was provided
            new_session_record = ChatSession(
                user_id=user_id,
                session_id=session_id_to_use, # Use the determined session_id
                history=history_to_return  # This will be an empty list for new sessions
            )
            db.add(new_session_record)
            db.commit()
            db.refresh(new_session_record)
            return session_id_to_use, history_to_return # history_to_return is empty here
        except Exception as e:
            db.rollback()
            # Consider logging the error: import logging; logging.exception("Error in get_or_create_session")
            raise e
        finally:
            db.close()

# Example Usage (for testing purposes, typically not here)
if __name__ == '__main__':
    # This requires the database to be initialized first.
    # Call init_db() from utils.py if you haven't.
    # from .utils import init_db
    # init_db() # Make sure this is safe to call multiple times or called once.

    print("Running ChatHistoryDBManager example...")
    manager = ChatHistoryDBManager()

    test_user_id = "user_test_123"
    
    # Test get_or_create_session (new session)
    created_session_id, history = manager.get_or_create_session(user_id=test_user_id)
    print(f"Created new session: ID='{created_session_id}', History={history}")
    assert history == []

    # Test save_history
    messages_to_save = [
        StandardMessage(role="user", content="Hello there!"),
        StandardMessage(role="assistant", content="Hi! How can I help you today?")
    ]
    manager.save_history(user_id=test_user_id, session_id=created_session_id, history=messages_to_save)
    print(f"Saved history to session '{created_session_id}'")

    # Test get_history
    retrieved_history = manager.get_history(session_id=created_session_id)
    print(f"Retrieved history: {retrieved_history}")
    assert len(retrieved_history) == 2
    assert retrieved_history[0].content == "Hello there!"
    assert retrieved_history[1].role == "assistant"

    # Test get_or_create_session (existing session)
    existing_session_id, existing_history = manager.get_or_create_session(user_id=test_user_id, session_id=created_session_id)
    print(f"Retrieved existing session: ID='{existing_session_id}', History={existing_history}")
    assert existing_session_id == created_session_id
    assert len(existing_history) == 2

    # Test updating history
    updated_messages = messages_to_save + [StandardMessage(role="user", content="What's the weather?")]
    manager.save_history(user_id=test_user_id, session_id=created_session_id, history=updated_messages)
    retrieved_updated_history = manager.get_history(session_id=created_session_id)
    print(f"Retrieved updated history: {retrieved_updated_history}")
    assert len(retrieved_updated_history) == 3
    
    # Test get_or_create_session with a non-existent session_id
    non_existent_session_id = str(uuid.uuid4())
    created_again_id, new_history = manager.get_or_create_session(user_id=test_user_id, session_id=non_existent_session_id)
    print(f"Tried to get non-existent session, created new: ID='{created_again_id}', History={new_history}")
    assert created_again_id == non_existent_session_id # Because we now create it if it doesn't exist with the given ID
    assert new_history == []
    
    # Verify it was created
    check_history = manager.get_history(session_id=created_again_id)
    assert check_history == []


    print("ChatHistoryDBManager example finished successfully.")
```

## File: src/vahan_sahayak/integrations/telematics.py
```python
"""
Telematics integration for Vahan Sahayak CLI.

Handles connection and health checks for the Telematics MCP server.
"""

import os
import logging
import asyncio
import aiohttp
import json
from typing import Tuple, Optional

# Configure logging
log_dir = "./logs/vahan_sahayak/integrations"
os.makedirs(log_dir, exist_ok=True)

debug_log_file = os.path.join(log_dir, "telematics_debug.log")
info_log_file = os.path.join(log_dir, "telematics_info.log")

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

if not logger.handlers:
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    debug_handler = logging.FileHandler(debug_log_file)
    debug_handler.setLevel(logging.DEBUG)
    debug_handler.setFormatter(formatter)
    logger.addHandler(debug_handler)

    info_handler = logging.FileHandler(info_log_file)
    info_handler.setLevel(logging.INFO)
    info_handler.setFormatter(formatter)
    logger.addHandler(info_handler)

    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(logging.INFO)
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)


class TelematicsIntegration:
    """Handles Telematics MCP connection and health."""

    def __init__(self, config):
        """Initialize Telematics Integration."""
        self.config = config
        self.debug_mode = config.get("DEBUG", False)
        self.base_url = self._get_base_url()
        logger.info(f"Initialized Telematics Integration with base URL: {self.base_url}")

    def _get_base_url(self) -> str:
        """
        Get the base URL for Telematics MCP based on debug mode.

        Returns:
            The base URL to use for Telematics MCP.
        """
        if self.debug_mode:
            base_url = "http://localhost:4747"
            logger.debug("Using localhost URL for Telematics MCP (debug mode)")
        else:
            # Use environment variables for host and port
            host_ip = os.environ.get("TELEMATICS_MCP_HOST_IP", "************")
            host_port = os.environ.get("TELEMATICS_MCP_HOST_PORT", "4747")
            base_url = f"http://{host_ip}:{host_port}"
            logger.debug(f"Using production URL for Telematics MCP: {base_url}")
        return base_url

    async def check_health(self) -> Tuple[bool, Optional[str]]:
        """
        Check if the Telematics MCP server is reachable and healthy.

        Returns:
            Tuple of (success, error_message).
        """
        if not self.base_url:
            return False, "Telematics MCP base URL is not configured."

        health_url = f"{self.base_url}/health"
        try:
            logger.debug(f"Checking Telematics MCP health at: {health_url}")
            timeout = aiohttp.ClientTimeout(total=5)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(health_url, ssl=not os.environ.get("PYTHONHTTPSVERIFY") == "0") as response:
                    if response.status == 200:
                        try:
                            data = await response.json()
                            if data.get("status", "").lower() == "ok":
                                logger.debug("Telematics MCP health check successful")
                                return True, None
                            else:
                                error_msg = f"Health endpoint returned unexpected status: {data.get('status', 'unknown')}"
                                logger.warning(f"Telematics MCP health check failed: {error_msg}")
                                return False, error_msg
                        except json.JSONDecodeError:
                            error_msg = "Health endpoint returned non-JSON response"
                            logger.warning(f"Telematics MCP health check failed: {error_msg}")
                            return False, error_msg
                    else:
                        error_msg = f"Server returned status code: {response.status}"
                        logger.warning(f"Telematics MCP health check failed: {error_msg}")
                        return False, error_msg
        except aiohttp.ClientConnectorError as e:
            error_msg = f"Connection error: {str(e)}"
            logger.error(f"Telematics MCP health check failed: {error_msg}")
            return False, error_msg
        except asyncio.TimeoutError:
            error_msg = "Connection timed out"
            logger.error(f"Telematics MCP health check failed: {error_msg}")
            return False, error_msg
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logger.error(f"Telematics MCP health check failed: {error_msg}")
            return False, error_msg

    # Add other Telematics related functions if any
```

## File: src/vahan_sahayak/core.py
```python
"""
Core logic for Vahan Sahayak CLI.

Contains the VahanSahayakCore class which manages integrations and the agent.
"""

import os
import json # Added import
import logging
import logging.handlers
from typing import Optional, List, Tuple, Dict, Any
from dataclasses import dataclass

from pydantic_ai import Agent
from pydantic_ai.mcp import MCPServerHTTP
from pydantic_ai.agent import InstrumentationSettings

# OpenTelemetry imports for Langfuse integration
from opentelemetry import trace  # Import trace

# Import model selector utility
from src.vahan_sahayak.utils.model_selector import ModelSelector # Import the class

# Import integrations
from src.vahan_sahayak.integrations.telematics import TelematicsIntegration
from src.vahan_sahayak.integrations.supabase import SupabaseIntegration
from src.vahan_sahayak.integrations.vector_tools import VectorToolsIntegration # Added import
from src.vahan_sahayak.integrations.langfuse import LangfuseIntegration

# Import prompts (assuming this remains external)
from src.prompts import get_formatted_system_prompt

# Configure logging
log_dir = "./logs/vahan_sahayak"
os.makedirs(log_dir, exist_ok=True)

debug_log_file = os.path.join(log_dir, "core_debug.log")
info_log_file = os.path.join(log_dir, "core_info.log")

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

if not logger.handlers:
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    debug_handler = logging.FileHandler(debug_log_file)
    debug_handler.setLevel(logging.DEBUG)
    debug_handler.setFormatter(formatter)
    logger.addHandler(debug_handler)

    info_handler = logging.FileHandler(info_log_file)
    info_handler.setLevel(logging.INFO)
    info_handler.setFormatter(formatter)
    logger.addHandler(info_handler)

    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(logging.INFO)
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)


@dataclass
class IntegrationStatus:
    """Status of a specific integration."""
    name: str
    available: bool
    error: Optional[str] = None


class VahanSahayakCore:
    """
    Core class for Vahan Sahayak.

    Manages integrations, agent state, and core logic.
    """

    def __init__(self, config):
        """
        Initialize VahanSahayakCore.

        Args:
            config: Configuration object/dictionary.
        """
        self.config = config
        self.model = config.get("LLM_MODEL_CHOICE", 'meta-llama/llama-4-maverick-17b-128e-instruct')
        self.model_name = self.model  # Store the clean model name (without provider prefix)
        self.temperature = float(config.get("LLM_TEMPERATURE", 0.2))
        self.max_tokens = int(config.get("LLM_MAX_TOKENS", 8192))

        self.telematics_integration: Optional[TelematicsIntegration] = None
        self.supabase_integration: Optional[SupabaseIntegration] = None
        self.vector_tools_integration: Optional[VectorToolsIntegration] = None # Added attribute
        self.langfuse_integration: Optional[LangfuseIntegration] = None

        self.integration_statuses: List[IntegrationStatus] = []

        self.agent: Optional[Agent] = None
        self.is_initialized: bool = False

        # Note: Initialization is now done separately via async methods
        logger.info("VahanSahayakCore instance created (not yet initialized)")

    async def initialize(self):
        """
        Asynchronously initialize the core components.
        This must be called after creating the VahanSahayakCore instance.
        """
        await self._initialize_integrations()
        self._initialize_agent()
        self.is_initialized = True
        logger.info("VahanSahayakCore fully initialized")

    async def _initialize_integrations(self):
        """Initializes and checks health of integrations."""
        logger.info("Initializing integrations")
        self.integration_statuses = []

        # Initialize Telematics Integration
        try:
            self.telematics_integration = TelematicsIntegration(self.config)
            is_available, error = await self.telematics_integration.check_health()
            self.integration_statuses.append(IntegrationStatus("Telematics", is_available, error))
            if is_available:
                logger.info("Telematics integration available.")
            else:
                logger.warning(f"Telematics integration unavailable: {error}")
        except Exception as e:
            logger.error(f"Error initializing Telematics integration: {e}")
            self.integration_statuses.append(IntegrationStatus("Telematics", False, str(e)))

        # Initialize Supabase Integration
        try:
            self.supabase_integration = SupabaseIntegration(self.config)
            is_available, error = await self.supabase_integration.check_health()
            self.integration_statuses.append(IntegrationStatus("Supabase", is_available, error))
            if is_available:
                logger.info("Supabase integration available.")
            else:
                logger.warning(f"Supabase integration unavailable: {error}")
        except Exception as e:
            logger.error(f"Error initializing Supabase integration: {e}")
            self.integration_statuses.append(IntegrationStatus("Supabase", False, str(e)))

        # Initialize Langfuse Integration
        try:
            self.langfuse_integration = LangfuseIntegration(self.config)
            is_available, error = await self.langfuse_integration.check_health()
            self.integration_statuses.append(IntegrationStatus("Langfuse", is_available, error))
            if is_available:
                logger.info("Langfuse integration available.")
            else:
                logger.warning(f"Langfuse integration unavailable: {error}")
        except Exception as e:
            logger.error(f"Error initializing Langfuse integration: {e}")
            self.integration_statuses.append(IntegrationStatus("Langfuse", False, str(e)))

        # Initialize Vector Tools Integration directly
        try:
            self.vector_tools_integration = VectorToolsIntegration(config=self.config)

            # Connect Langfuse integration if available
            if self.langfuse_integration and self._is_integration_available("Langfuse"):
                self.vector_tools_integration.set_langfuse_integration(self.langfuse_integration)
                logger.info("Connected Langfuse integration to VectorTools for tracing.")

            # Check health to verify the client is working properly
            is_available, error = await self.vector_tools_integration.check_health()
            self.integration_statuses.append(IntegrationStatus("VectorTools", is_available, error))

            if is_available:
                logger.info("VectorTools integration available.")
            else:
                logger.warning(f"VectorTools integration unavailable: {error}")
        except Exception as e:
            logger.error(f"Failed to initialize VectorToolsIntegration: {e}")
            self.vector_tools_integration = None # Ensure it's None on failure
            self.integration_statuses.append(IntegrationStatus("VectorTools", False, str(e)))

        logger.info("Integration initialization complete.")

    def _initialize_agent(self):
        """Initializes the Pydantic AI agent."""
        logger.info("Initializing agent")

        mcp_servers = []
        tools = []
        tracer = None
        instrumentation_settings = None

        # Add MCP servers based on integration availability and config flags
        if self.config.get("USE_MCP_SERVERS", True):
            if self.config.get("ENABLE_TELEMATICS_MCP", True) and self._is_integration_available("Telematics"):
                # Assuming TelematicsIntegration provides an MCP server instance or URL
                # Need to adapt based on how TelematicsIntegration is structured
                # For now, assuming it provides a base_url that can be used to create MCPServerHTTP
                telematics_url = self.telematics_integration.base_url if self.telematics_integration else None
                if telematics_url:
                    try:
                        # Check if the URL already ends with /sse to avoid duplication
                        if telematics_url.endswith("/sse"):
                            telematics_mcp_url = telematics_url
                        else:
                            telematics_mcp_url = f"{telematics_url}/sse"

                        logger.info(f"Telematics MCP URL: {telematics_mcp_url}")
                        mcp_server = MCPServerHTTP(url=telematics_mcp_url, timeout=10, sse_read_timeout=300)
                        mcp_servers.append(mcp_server)
                        logger.debug(f"Added Telematics MCP server: {telematics_mcp_url}")
                    except Exception as e:
                        logger.error(f"Failed to create Telematics MCP server instance: {e}")
                        # Mark Telematics as unavailable if server instance creation fails? Or handle in integration?
                        pass  # Continue without this MCP server

            if self.config.get("ENABLE_SUPABASE_MCP", True) and self._is_integration_available("Supabase"):
                # Assuming SupabaseIntegration provides an MCP server instance or URL
                supabase_mcp_url = self.supabase_integration.supabase_mcp_base_url if self.supabase_integration else None
                if supabase_mcp_url:
                    try:
                        # Check if the URL already ends with /sse to avoid duplication
                        if supabase_mcp_url.endswith("/sse"):
                            mcp_url = supabase_mcp_url
                        else:
                            mcp_url = f"{supabase_mcp_url}/sse"

                        mcp_server = MCPServerHTTP(url=mcp_url, timeout=10, sse_read_timeout=300)
                        mcp_servers.append(mcp_server)
                        logger.debug(f"Added Supabase MCP server: {mcp_url}")
                    except Exception as e:
                        logger.error(f"Failed to create Supabase MCP server instance: {e}")
                        pass  # Continue without this MCP server

        # Add Vector tools if VectorToolsIntegration is available and has a valid client
        if self.vector_tools_integration and self.vector_tools_integration.client:
            logger.info("VectorToolsIntegration with Supabase client available, attempting to add vector tools.")
            try:
                tools.append(self.vector_tools_integration.retrieve_documents)
                logger.debug("Added retrieve_documents tool.")
            except AttributeError:
                logger.warning("Could not add retrieve_documents tool: Method not found in VectorToolsIntegration.")
            except Exception as e:
                logger.warning(f"Could not add retrieve_documents tool: {e}")

            try:
                tools.append(self.vector_tools_integration.retrieve_feedback)
                logger.debug("Added retrieve_feedback tool.")
            except AttributeError:
                logger.warning("Could not add retrieve_feedback tool: Method not found in VectorToolsIntegration.")
            except Exception as e:
                logger.warning(f"Could not add retrieve_feedback tool: {e}")
        else:
            logger.warning("VectorToolsIntegration with valid Supabase client not available, skipping vector tool addition.")

        # Configure instrumentation if Langfuse integration is available
        if self._is_integration_available("Langfuse") and self.langfuse_integration:
            tracer = self.langfuse_integration.get_tracer()
            if tracer:
                logger.info("Langfuse tracer available, configuring instrumentation.")
                try:
                    instrumentation_settings = InstrumentationSettings(
                        tracer_provider=trace.get_tracer_provider(),
                        event_mode="logs",
                    )
                    logger.debug("Instrumentation settings configured.")
                except Exception as e:
                    logger.error(f"Error configuring instrumentation settings: {e}")
                    instrumentation_settings = None  # Disable instrumentation on error
            else:
                logger.warning("Langfuse tracer not available, instrumentation disabled.")
        else:
            logger.warning("Langfuse integration not available, instrumentation disabled.")

        # Determine model and settings using the ModelSelector class
        provider_name: Optional[str] = None
        base_url: Optional[str] = None
        api_key: Optional[str] = None
        model_specific_settings: Dict[str, Any] = {}

        # Use ModelSelector to create the appropriate model and get settings
        selector = ModelSelector()

        # Get model configuration
        provider_name, base_url, api_key, returned_clean_model_name, model_specific_settings = selector.get_model_config(self.model)

        if not api_key:
            logger.warning(f"API key for {provider_name} was not resolved by ModelSelector. Agent creation might fail if required.")
            raise ValueError(f"API key for {provider_name} is missing.")

        self.model_name = returned_clean_model_name  # Use the clean name from selector
        logger.info(f"Successfully configured model '{self.model_name}' via provider '{provider_name}' ({base_url}) using ModelSelector.")

        # Get model settings with appropriate token limits
        try:
            model_settings = selector.get_model_settings(
                self.model,
                default_max_tokens=self.max_tokens,
                default_temperature=self.temperature
            )
            logger.debug(f"Using model settings: max_tokens={model_settings.get('max_tokens', 'N/A')}, temperature={model_settings.get('temperature', 'N/A')}")
        except Exception as e:
            logger.error(f"Error getting model settings: {e}")
            raise # Re-raise the exception to stop agent creation

        # Create the agent
        try:
            # Create the agent
            logger.info(f"Creating agent with {len(mcp_servers)} MCP servers and {len(tools)} tools.")
            # Create the model using the ModelSelector
            try:
                # Use the ModelSelector to create the appropriate model instance
                logger.debug(f"Creating model for provider: '{provider_name}', model_name: '{self.model_name}'")
                model = selector.create_pydantic_ai_model(self.model)
                logger.info(f"Successfully created model for '{self.model_name}' with provider '{provider_name}'")

            except ImportError as ie:
                logger.error(f"Failed to import model/provider classes for {provider_name}: {ie}", exc_info=True)
                raise
            except Exception as model_creation_error:
                logger.error(f"Error creating model instance for provider '{provider_name}', model '{self.model_name}': {model_creation_error}", exc_info=True)
                raise

            self.agent = Agent(
                model, # Use the created model instance or identifier string
                mcp_servers=mcp_servers,
                tools=tools,
                system_prompt=get_formatted_system_prompt(),  # Using system_prompt instead of instructions
                model_settings=model_settings,
                instrument=instrumentation_settings,  # Will be None if Langfuse is off/failed
            )
            logger.info("Agent created successfully.")
        except Exception as e:
            logger.critical(f"Failed to create agent: {e}")
            self.agent = None  # Ensure agent is None on failure
            raise

    def _is_integration_available(self, name: str) -> bool:
        """Checks if a specific integration is available."""
        for status in self.integration_statuses:
            if status.name == name:
                return status.available
        return False  # Integration not found in status list

    def get_integration_status(self, name: str) -> Optional[IntegrationStatus]:
        """Gets the status of a specific integration."""
        for status in self.integration_statuses:
            if status.name == name:
                return status
        return None

    def get_all_integration_statuses(self) -> List[IntegrationStatus]:
        """Gets the status of all integrations."""
        return self.integration_statuses

    async def run_query(self, query: str, history: List[Dict[str, Any]]) -> Tuple[str, List[Dict[str, Any]]]:
        """
        Runs a query through the agent.

        Args:
            query: The user query.
            history: The conversation history.

        Returns:
            Tuple of (response_text, updated_history).
        """
        if not self.agent:
            logger.error("Agent is not initialized, cannot run query.")
            return "Error: Agent is not initialized.", history

        logger.info(f"Running query: '{query[:50]}{'...' if len(query) > 50 else ''}'")

        # Get the tracer from Langfuse integration if available
        tracer = None
        if self.langfuse_integration and self.langfuse_integration.get_tracer():
            tracer = self.langfuse_integration.get_tracer()

        try:
            # UsageLimits was defined but not used.
            # usage_limits = UsageLimits(
            #     response_tokens_limit=int(self.config.get("LLM_RESPONSE_TOKENS_LIMIT", 32768)),
            #     request_tokens_limit=int(self.config.get("LLM_REQUEST_TOKENS_LIMIT", 32768)),
            #     request_limit=int(self.config.get("LLM_REQUEST_LIMIT", 10))
            # )

            # Create a span for the entire query if tracer is available
            if tracer:
                with tracer.start_as_current_span(
                    "agent_query",
                    kind=trace.SpanKind.CLIENT
                ) as span:
                    # Add query as an attribute
                    span.set_attribute("input", query)
                    span.set_attribute("gen.ai.request", query)

                    # Use the run_mcp_servers context manager as required by pydantic-ai
                    async with self.agent.run_mcp_servers():
                        result = await self.agent.run(query, message_history=history)#, usage_limits=usage_limits)

                        # Add response as an attribute
                        span.set_attribute("output", result.output)
                        span.set_attribute("gen.ai.response", result.output)
                        span.set_status(trace.Status(trace.StatusCode.OK))

                        logger.info("Query executed successfully.")
                        # Convert pydantic-ai message objects to dicts
                        updated_history_dicts = self._convert_messages_to_dicts(result.all_messages())

                        return result.output, updated_history_dicts
            else:
                # No tracer available, just run the query
                # Log the input message history for debugging
                logger.debug(f"Input message history (type: {type(history)}, length: {len(history)}):")
                for i, msg_item in enumerate(history):
                    logger.debug(f"  History item {i} (type: {type(msg_item)}): {msg_item}")

                try:
                    async with self.agent.run_mcp_servers():
                        # Add debug logging for the agent run
                        logger.debug(f"Running agent with query: '{query}' and message_history of length {len(history)}")

                        # Try to inspect the agent's model type
                        if hasattr(self.agent, 'model'):
                            logger.debug(f"Agent model type before run: {type(self.agent.model).__name__}")
                        else:
                            logger.debug("Agent model attribute not found before run.")

                        # Run the agent with specific error handling for assertion errors
                        try:
                            result = await self.agent.run(query, message_history=history)#, usage_limits=usage_limits)
                        except AssertionError as assertion_error:
                            # Extract the message from the assertion error
                            error_msg = str(assertion_error)
                            logger.error(f"AssertionError in agent.run: {error_msg}")

                            # Check if it's the specific "Expected code to be unreachable" error
                            if "Expected code to be unreachable" in error_msg:
                                # Try to extract the problematic message format
                                import re
                                match = re.search(r"but got: (.+)$", error_msg)
                                if match:
                                    problematic_format = match.group(1)
                                    logger.error(f"Problematic message format: {problematic_format}")

                                    # Log detailed diagnostic information
                                    logger.error("This error typically occurs when the message format doesn't match what pydantic-ai expects.")
                                    logger.error("It might be due to a version mismatch or incompatibility between model providers.")

                                    # Try to provide a workaround suggestion
                                    logger.error("Suggested workaround: Try using a different model provider or check pydantic-ai version compatibility.")

                            # Re-raise the error
                            raise

                        # Log successful execution
                        logger.info("Query executed successfully.")

                        # Debug log the result structure
                        logger.debug(f"Result type: {type(result).__name__}")
                        if hasattr(result, 'all_messages'):
                            messages = result.all_messages()
                            logger.debug(f"Result contains {len(messages)} messages")

                            # Log the first message to see its structure
                            if messages:
                                first_msg = messages[0]
                                logger.debug(f"First message type: {type(first_msg).__name__}")
                                logger.debug(f"First message attributes: {dir(first_msg)}")

                                # Try to inspect the message structure more deeply
                                if hasattr(first_msg, 'parts') and first_msg.parts:
                                    logger.debug(f"First message has {len(first_msg.parts)} parts")
                                    logger.debug(f"First part type: {type(first_msg.parts[0]).__name__}")
                except Exception as run_error:
                    logger.error(f"Error during agent.run: {run_error}", exc_info=True)
                    raise

                # Convert pydantic-ai message objects to dicts
                updated_history_dicts = self._convert_messages_to_dicts(result.all_messages())

                return result.output, updated_history_dicts
        except Exception as e:
            logger.error(f"Error running agent query. Exception type: {type(e)}, Exception str: {str(e)}, Exception repr: {repr(e)}") # Detailed log
            # Record the error in the span if tracer is available
            if tracer and trace.get_current_span():
                span = trace.get_current_span()
                span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
                span.record_exception(e)
            return f"Error processing query: {e}", history

    def change_model(self, new_model: str):
        """
        Changes the LLM model and re-initializes the agent.

        Args:
            new_model: The name of the new model to use.
        """
        logger.info(f"Changing model from {self.model} to {new_model}")
        try:
            # Store the old model in case we need to revert
            old_model = self.model
            self.model = new_model

            # Add detailed logging for debugging
            logger.debug(f"Model change details: old={old_model}, new={new_model}")

            # Check if the model exists in providers.json
            try:
                selector = ModelSelector()
                # Just check if the model exists, we don't need to use the returned values
                selector.get_model_config(new_model)
                logger.debug(f"Model '{new_model}' found in providers.json")
            except Exception as e:
                logger.warning(f"Model not found in providers.json: {e}")

            # Initialize the agent with the new model
            self._initialize_agent()
            logger.info(f"Model successfully changed to {self.model}")
        except Exception as e:
            logger.error(f"Error changing model to {new_model}: {e}", exc_info=True)
            # If there was an error, try to revert to the previous model
            try:
                if 'old_model' in locals():
                    logger.warning(f"Attempting to revert to previous model: {old_model}")
                    self.model = old_model
                    self._initialize_agent()
                    logger.info(f"Successfully reverted to model {old_model}")
                else:
                    logger.error("Could not revert to previous model: old model not defined")
            except Exception as revert_error:
                logger.error(f"Error reverting to previous model: {revert_error}", exc_info=True)

            # Re-raise the original exception
            raise

    def change_temperature(self, new_temperature: float):
        """Changes the LLM temperature and updates agent settings."""
        logger.info(f"Changing temperature from {self.temperature} to {new_temperature}")
        self.temperature = new_temperature
        if self.agent and self.agent.model_settings:
            try:
                self.agent.model_settings.temperature = new_temperature
                logger.debug("Agent model settings updated with new temperature.")
            except Exception as e:
                logger.warning(f"Could not update agent model settings temperature directly: {e}. Re-initializing agent.")
                self._initialize_agent()  # Fallback to re-init
        else:
            logger.warning("Agent or model settings not available, re-initializing agent for temperature change.")
            self._initialize_agent()
        logger.info(f"Temperature changed to {self.temperature}")

    def change_max_tokens(self, new_max_tokens: int):
        """Changes the LLM max tokens and updates agent settings."""
        logger.info(f"Changing max tokens from {self.max_tokens} to {new_max_tokens}")
        self.max_tokens = new_max_tokens
        if self.agent and self.agent.model_settings:
            try:
                self.agent.model_settings.max_tokens = new_max_tokens
                logger.debug("Agent model settings updated with new max tokens.")
            except Exception as e:
                logger.warning(f"Could not update agent model settings max tokens directly: {e}. Re-initializing agent.")
                self._initialize_agent()  # Fallback to re-init
        else:
            logger.warning("Agent or model settings not available, re-initializing agent for max tokens change.")
            self._initialize_agent()
        logger.info(f"Max tokens changed to {self.max_tokens}")

    def _convert_messages_to_dicts(self, messages: List[Any]) -> List[Dict[str, Any]]:
        """
        Converts a list of pydantic-ai BaseMessage objects (or similar) to a list of dictionaries
        suitable for session history storage or API responses, matching StandardMessage structure.

        Args:
            messages: A list of message objects (e.g., from result.all_messages()).

        Returns:
            A list of dictionaries, where each dictionary represents a message.
        """
        result_list = [] # Renamed from result to avoid conflict with pydantic-ai's result object
        valid_roles = {"user", "assistant", "system", "tool"}
        for msg in messages:
            role = getattr(msg, 'role', None)
            if role not in valid_roles:
                logger.warning(f"Skipping message with invalid or missing role: {role} | msg: {msg}")
                continue
            output_dict: Dict[str, Any] = {"role": role}

            # Handle content
            if hasattr(msg, 'content') and msg.content is not None:
                content_val = msg.content # Renamed to avoid conflict with outer 'content' variable in prompt
                # If content_val is a list (e.g., Google's Parts), try to extract text or stringify
                if isinstance(content_val, list):
                    text_content_parts = []
                    has_non_text_parts = False
                    for part in content_val:
                        if isinstance(part, dict) and "text" in part:
                            text_content_parts.append(part["text"])
                        elif hasattr(part, "text"): # For Part objects
                             text_content_parts.append(part.text)
                        elif isinstance(part, str): # If a part is just a string
                            text_content_parts.append(part)
                        else: # Non-text part, like FunctionCall or FunctionResponse
                            has_non_text_parts = True
                            break
                    
                    if not has_non_text_parts and text_content_parts:
                        output_dict["content"] = "\n".join(text_content_parts)
                    elif has_non_text_parts or not text_content_parts:
                        if not (hasattr(msg, 'tool_calls') and msg.tool_calls) and \
                           not (hasattr(msg, 'tool_call_id') and msg.tool_call_id):
                            try:
                                output_dict["content"] = json.dumps(content_val)
                            except Exception:
                                output_dict["content"] = str(content_val)
                        # else content might be None if tool_calls are present
                elif isinstance(content_val, (dict, list)): # General dict/list content_val
                    try:
                        output_dict["content"] = json.dumps(content_val)
                    except Exception:
                        output_dict["content"] = str(content_val)
                else: # Simple string content_val
                    output_dict["content"] = str(content_val)
            elif not (hasattr(msg, 'tool_calls') and msg.tool_calls):
                 output_dict["content"] = None


            # Handle name (for tool role)
            if hasattr(msg, 'name') and msg.name:
                output_dict["name"] = msg.name
            
            # Handle tool_calls (for assistant role)
            if hasattr(msg, 'tool_calls') and msg.tool_calls:
                output_dict["tool_calls"] = msg.tool_calls
                if output_dict.get("role") == "assistant":
                    output_dict["content"] = None


            # Handle tool_call_id (for tool role)
            if hasattr(msg, 'tool_call_id') and msg.tool_call_id:
                output_dict["tool_call_id"] = msg.tool_call_id
                if "name" not in output_dict and output_dict.get("role") == "tool":
                    logger.warning(f"Tool message (ID: {msg.tool_call_id}) missing 'name'. This might be an issue.")

            if output_dict.get("role") == "assistant" and "tool_calls" in output_dict and output_dict.get("content") is not None:
                logger.debug("Assistant message has tool_calls, ensuring content is None.")
                output_dict["content"] = None
            
            result_list.append(output_dict)
        return result_list


if __name__ == "__main__":
    import asyncio
    from src.vahan_sahayak.config import load_config
    async def main():
        pass


    asyncio.run(main())
```
