# Plan: Implement order_list Tool and Endpoint (24 May 2025, 12:27)

## Objective
Implement an `order_list` tool in `src/integrations/tool_module.py` that:
- Takes a phone number as input
- Calls the user details endpoint to get the tenant UUID
- Calls the order list endpoint with the tenant UUID to get the user's orders
- Handles errors and edge cases (invalid phone, no orders, etc.)
- Is usable as a tool by the agent (like get_vehicle_quick_stat)
- Is also exposed as a FastAPI endpoint in `src/core.py`

## Steps
1. Add `get_order_list(phone_number: str)` to `ToolManager` in `tool_module.py`.
2. Implement the two-step API call logic as per `order_list_plan.md`:
   - POST to `http://fury-arc-8080a.corp.olaelectric.com/v1/internal/user/2w/details/phone` with phone number and required headers to get tenant UUID.
   - GET to `https://arcee-8080a.ev.corp.olaelectric.com/v1/order/{tenant_uuid}` with required query params and headers to get orders.
3. Handle and log errors (invalid phone, missing uuid, no orders, etc.).
4. Add a FastAPI endpoint `/order_list` in `src/core.py` that takes a phone number and returns the order list (using the tool).
5. Register the tool in the agent (if not already auto-registered).
6. Add/update tests for the new tool and endpoint.
7. Update documentation if needed. 