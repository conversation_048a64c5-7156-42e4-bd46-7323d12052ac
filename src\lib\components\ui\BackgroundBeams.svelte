<script lang="ts">
	export let className: string | undefined = undefined;
</script>

<!-- Aurora Background: animated gradient using Tailwind and keyframes -->
<div class={className + " absolute inset-0 z-0 overflow-hidden"}>
	<div class="absolute inset-0 w-full h-full animate-aurora bg-gradient-to-br from-green-400 via-teal-400 to-lime-300 opacity-70 blur-2xl"></div>
	<style>
		@keyframes aurora {
			0% {
				filter: blur(32px) brightness(1.1);
				background-position: 0% 50%;
			}
			50% {
				filter: blur(48px) brightness(1.3);
				background-position: 100% 50%;
			}
			100% {
				filter: blur(32px) brightness(1.1);
				background-position: 0% 50%;
			}
		}
		.animate-aurora {
			animation: aurora 12s ease-in-out infinite;
			background-size: 200% 200%;
		}
	</style>
</div> 