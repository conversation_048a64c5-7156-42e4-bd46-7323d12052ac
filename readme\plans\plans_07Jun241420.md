# <PERSON><PERSON> Sa<PERSON> Auth Page Implementation Plan (07Jun241420)

## Overview
Implement the `/auth` route in SvelteKit using Svelte Shadcn and Aceternity UI components. The page will have a left-side image with a background effect and right-side forms (login, signup, reset password) using modern UI components.

## Steps
1. Scaffold `/auth` page with two-column layout.
2. Implement left side: image with background effect (Background Boxes/Beams).
3. Implement right side: form with toggle (login/signup/reset) using Svelte Shadcn/Aceternity components.
4. Style for responsiveness and modern look.
5. Ensure all UI components are reusable and follow best practices. 