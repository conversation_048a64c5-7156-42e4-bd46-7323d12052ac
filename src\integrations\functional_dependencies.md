# Functional Dependencies for integrations module

## ToolManager

- `__init__`: Initializes Gemini and Supabase clients using environment variables.
- `check_health`: Checks Supabase connection health.
- `get_embeddings`: Calls Gemini API to get text embeddings. Depends on Gemini API key and `google.generativeai` package.
- `get_tools`: (To be implemented) Retrieves available tools.

# Functional Dependencies: Mem0Manager (as of 12 May 2025, 12:08)

## Class: Mem0Manager
- Uses: mem0.Memory (with Gemini 768 config)
- Logging: ./logs/integrations/info.log, ./logs/integrations/debug.log

### Methods

#### add(user_id: str, messages: List[Dict[str, str]], metadata: Optional[Dict[str, Any]] = None) -> Any
- Input: user_id (str), messages (list of dicts with 'role' and 'content'), optional metadata (dict)
- Output: Result from Mem0 add operation

#### retrieve(query: str, user_id: str) -> Any
- Input: query (str), user_id (str)
- Output: Search results from Mem0

#### update(memory_id: str, data: str) -> Any
- Input: memory_id (str), data (str)
- Output: Result from Mem0 update operation

#### delete(memory_id: Optional[str] = None, user_id: Optional[str] = None) -> None
- Input: memory_id (str, optional), user_id (str, optional)
- Output: None (performs delete operation in Mem0)

## Notes
- All methods log actions to info and debug logs.
- No collection or advanced config logic; API is as simple as Mem0 Quick Start.

```mermaid
graph TD
    ToolManager --> get_embeddings
    ToolManager --> check_health
    ToolManager --> get_tools
    get_embeddings --> |env| GEMINI_API_KEY
    get_embeddings --> |pkg| google.generativeai
    get_embeddings --> |api| Gemini API
    check_health --> |db| Supabase
``` 