from loguru import logger
import asyncio
import aiohttp
import json
import base64
import os
from typing import Tuple, Optional
from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.trace.export import SimpleSpanProcessor
from dotenv import load_dotenv
import functools
import inspect

# Logging setup as per project rules
LOG_DIR = os.path.join(os.path.dirname(__file__), '../../logs/langfuse')
os.makedirs(LOG_DIR, exist_ok=True)
logger.add(os.path.join(LOG_DIR, 'info.log'), level='INFO', rotation='10 MB', retention='10 days')
logger.add(os.path.join(LOG_DIR, 'debug.log'), level='DEBUG', rotation='10 MB', retention='10 days')

load_dotenv()


class LangfuseIntegration:
    """Handles Langfuse telemetry connection and health."""

    def __init__(self, config):
        """Initialize Langfuse Integration."""
        self.config = config
        self.debug_mode = config.get("LANGFUSE_DEBUG", False)
        self.public_key = config.get("LANGFUSE_public_key")
        self.secret_key = config.get("LANGFUSE_secret_key")
        self.base_url = self._get_base_url()
        self.auth_token = self._get_auth_token() if self.public_key and self.secret_key else None
        self.tracer = None

        if self.public_key and self.secret_key:
            try:
                self._initialize_tracer()
                logger.info("Langfuse tracer initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Langfuse tracer: {e}")
        else:
            logger.warning("Langfuse credentials not provided")

        logger.info(f"Initialized Langfuse Integration with base URL: {self.base_url}")

    def _get_base_url(self) -> str:
        """
        Get the base URL for Langfuse based on debug mode.

        Returns:
            The base URL to use for Langfuse.
        """
        if self.debug_mode:
            base_url = "http://localhost:3000"
            logger.debug("Using localhost URL for Langfuse (debug mode)")
        else:
            # Use environment variables for host and port
            host_ip = os.environ.get("LANGFUSE_HOST_IP", "************")
            host_port = os.environ.get("LANGFUSE_HOST_PORT", "3000")
            base_url = f"http://{host_ip}:{host_port}"
            logger.debug(f"Using production URL for Langfuse: {base_url}")
        return base_url

    def _get_auth_token(self) -> str:
        """
        Generate the Langfuse authentication token.

        Returns:
            Base64 encoded authentication token.
        """
        if not self.public_key or not self.secret_key:
            return ""
        return base64.b64encode(
            f"{self.public_key}:{self.secret_key}".encode()
        ).decode()

    def _initialize_tracer(self):
        """Initialize the OpenTelemetry tracer for Langfuse."""
        if not self.auth_token:
            logger.warning("Cannot initialize tracer: missing authentication token")
            return

        try:
            # Configure OpenTelemetry exporter
            os.environ["OTEL_EXPORTER_OTLP_ENDPOINT"] = f"{self.base_url}/api/public/otel"
            os.environ["OTEL_EXPORTER_OTLP_HEADERS"] = f"Authorization=Basic {self.auth_token}"

            # Set additional OpenTelemetry environment variables for better tracing
            os.environ["OTEL_RESOURCE_ATTRIBUTES"] = "service.name=vahan_sahayak"
            os.environ["OTEL_TRACES_EXPORTER"] = "otlp"
            os.environ["OTEL_EXPORTER_OTLP_TRACES_PROTOCOL"] = "http/protobuf"
            os.environ["OTEL_EXPORTER_OTLP_TRACES_ENDPOINT"] = f"{self.base_url}/api/public/otel/v1/traces"

            # Set up trace provider
            trace_provider = TracerProvider()
            trace_provider.add_span_processor(SimpleSpanProcessor(OTLPSpanExporter()))
            trace.set_tracer_provider(trace_provider)

            # Create a tracer
            self.tracer = trace.get_tracer("vahan_sahayak")
            logger.debug("Langfuse tracer initialized")
        except Exception as e:
            logger.error(f"Error initializing tracer: {e}")
            self.tracer = None
            raise

    async def check_health(self) -> Tuple[bool, Optional[str]]:
        """
        Check if the Langfuse service is reachable and healthy.

        Returns:
            Tuple of (success, error_message).
        """
        if not self.base_url:
            return False, "Langfuse base URL is not configured."

        if not self.auth_token:
            return False, "Langfuse authentication token is not available."

        health_url = f"{self.base_url}/api/public/health"
        try:
            logger.debug(f"Checking Langfuse health at: {health_url}")
            timeout = aiohttp.ClientTimeout(total=5)
            headers = {"Authorization": f"Basic {self.auth_token}"}

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(health_url, headers=headers, ssl=not os.environ.get("PYTHONHTTPSVERIFY") == "0") as response:
                    if response.status == 200:
                        try:
                            data = await response.json()
                            if data.get("status", "").lower() == "ok":
                                logger.debug("Langfuse health check successful")
                                return True, None
                            else:
                                error_msg = f"Health endpoint returned unexpected status: {data.get('status', 'unknown')}"
                                logger.warning(f"Langfuse health check failed: {error_msg}")
                                return False, error_msg
                        except json.JSONDecodeError:
                            error_msg = "Health endpoint returned non-JSON response"
                            logger.warning(f"Langfuse health check failed: {error_msg}")
                            return False, error_msg
                    else:
                        error_msg = f"Server returned status code: {response.status}"
                        logger.warning(f"Langfuse health check failed: {error_msg}")
                        return False, error_msg
        except aiohttp.ClientConnectorError as e:
            error_msg = f"Connection error: {str(e)}"
            logger.error(f"Langfuse health check failed: {error_msg}")
            return False, error_msg
        except asyncio.TimeoutError:
            error_msg = "Connection timed out"
            logger.error(f"Langfuse health check failed: {error_msg}")
            return False, error_msg
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logger.error(f"Langfuse health check failed: {error_msg}")
            return False, error_msg

    def get_tracer(self):
        """
        Get the OpenTelemetry tracer instance.

        Returns:
            The tracer instance or None if not initialized.
        """
        return self.tracer

    def trace_tool_call(self, tool_name):
        """
        Decorator to trace tool calls with proper input and output capture.
        Handles both async and sync functions.
        """
        def decorator(func):
            if inspect.iscoroutinefunction(func):
                # Async version
                @functools.wraps(func)
                async def async_wrapper(*args, **kwargs):
                    if not self.tracer:
                        return await func(*args, **kwargs)
                    with self.tracer.start_as_current_span(
                        f"running tool: {tool_name}",
                        kind=trace.SpanKind.INTERNAL
                    ) as span:
                        span.set_attribute("gen.ai.tool.name", tool_name)
                        span.set_attribute("gen.ai.tool.call.id", f"call_{id(func)}")
                        input_json = json.dumps(kwargs, default=str)
                        span.set_attribute("input", input_json)
                        for key, value in kwargs.items():
                            if isinstance(value, (str, int, float, bool)) or value is None:
                                span.set_attribute(f"tool_arguments.{key}", str(value))
                        try:
                            result = await func(*args, **kwargs)
                            output_str = str(result)
                            if len(output_str) > 1000:
                                output_str = output_str[:997] + "..."
                            span.set_attribute("output", output_str)
                            span.set_status(trace.Status(trace.StatusCode.OK))
                            return result
                        except Exception as e:
                            span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
                            span.record_exception(e)
                            raise
                return async_wrapper
            else:
                # Sync version
                @functools.wraps(func)
                def sync_wrapper(*args, **kwargs):
                    if not self.tracer:
                        return func(*args, **kwargs)
                    with self.tracer.start_as_current_span(
                        f"running tool: {tool_name}",
                        kind=trace.SpanKind.INTERNAL
                    ) as span:
                        span.set_attribute("gen.ai.tool.name", tool_name)
                        span.set_attribute("gen.ai.tool.call.id", f"call_{id(func)}")
                        input_json = json.dumps(kwargs, default=str)
                        span.set_attribute("input", input_json)
                        for key, value in kwargs.items():
                            if isinstance(value, (str, int, float, bool)) or value is None:
                                span.set_attribute(f"tool_arguments.{key}", str(value))
                        try:
                            result = func(*args, **kwargs)
                            output_str = str(result)
                            if len(output_str) > 1000:
                                output_str = output_str[:997] + "..."
                            span.set_attribute("output", output_str)
                            span.set_status(trace.Status(trace.StatusCode.OK))
                            return result
                        except Exception as e:
                            span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
                            span.record_exception(e)
                            raise
                return sync_wrapper
        return decorator

    # Add other Langfuse related functions if any
