### Handle within ./src/agent.py

```log
PS C:\Users\<USER>\codes\vahan_sahayak_fastapi_simple> uv run .\src\agent.py
The capital of France is Paris.
Traceback (most recent call last):
  File "C:\Users\<USER>\codes\vahan_sahayak_fastapi_simple\src\agent.py", line 66, in <module>
    asyncio.run(main())
    ~~~~~~~~~~~^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.0-windows-x86_64-none\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.0-windows-x86_64-none\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.0-windows-x86_64-none\Lib\asyncio\base_events.py", line 721, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\codes\vahan_sahayak_fastapi_simple\src\agent.py", line 63, in main
    async with or_agent.run_stream('What is the capital of the UK?') as response:
               ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.0-windows-x86_64-none\Lib\contextlib.py", line 214, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\codes\vahan_sahayak_fastapi_simple\.venv\Lib\site-packages\pydantic_ai\agent.py", line 953, in run_stream
    async with node._stream(graph_ctx) as streamed_response:  # pyright: ignore[reportPrivateUsage]
               ~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.0-windows-x86_64-none\Lib\contextlib.py", line 214, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\codes\vahan_sahayak_fastapi_simple\.venv\Lib\site-packages\pydantic_ai\_agent_graph.py", line 300, in _stream
    async with ctx.deps.model.request_stream(
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        ctx.state.message_history, model_settings, model_request_parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ) as streamed_response:
    ^
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.0-windows-x86_64-none\Lib\contextlib.py", line 214, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\codes\vahan_sahayak_fastapi_simple\.venv\Lib\site-packages\pydantic_ai\models\openai.py", line 214, in request_stream
    yield await self._process_streamed_response(response)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\codes\vahan_sahayak_fastapi_simple\.venv\Lib\site-packages\pydantic_ai\models\openai.py", line 312, in _process_streamed_response
    first_chunk = await peekable_response.peek()
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\codes\vahan_sahayak_fastapi_simple\.venv\Lib\site-packages\pydantic_ai\_utils.py", line 251, in peek
    self._buffer = await self._source_iter.__anext__()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\codes\vahan_sahayak_fastapi_simple\.venv\Lib\site-packages\openai\_streaming.py", line 147, in __aiter__
    async for item in self._iterator:
        yield item
  File "C:\Users\<USER>\codes\vahan_sahayak_fastapi_simple\.venv\Lib\site-packages\openai\_streaming.py", line 174, in __stream__
    raise APIError(
    ...<3 lines>...
    )
openai.APIError: This request requires more credits, or fewer max_tokens. You requested up to 8192 tokens, but can only afford 2118. To increase, visit https://openrouter.ai/settings/credits and add more credits
```