<script lang="ts">
import VanishingChatSuggestions from '$lib/components/ui/VanishingChatSuggestions.svelte';
import Sun from "lucide-svelte/icons/sun";
import Moon from "lucide-svelte/icons/moon";
import { toggleMode } from "mode-watcher";
import { But<PERSON> } from "$lib/components/ui/button/index.js";
import { logger } from '$lib/utils';
import { LogOut } from 'lucide-svelte';
import { auth } from '$lib/services/firebase.config.js';
import { signOut } from 'firebase/auth';
import { goto } from '$app/navigation';
import { GridAndDotBackgrounds, Spotlight } from '$lib/components/ui/GridAndDotBackgrounds';
import { Root as Slider } from '$lib/components/ui/slider';
import { Root as SelectRoot, Trigger as SelectTrigger, Content as SelectContent, Group as SelectGroup, Label as SelectLabel, Item as SelectItem } from '$lib/components/ui/select';
import { PROVIDERS } from '$lib/config';
import { onMount } from 'svelte';
import { Badge } from '$lib/components/ui/badge/index.js';
import UserMessage from '$lib/components/ui/UserMessage.svelte';
import ResponseMessage from '$lib/components/ui/ResponseMessage.svelte';
import { initApiService, sendChatRequest, validateVIN, validatePhone, validateOrderId, type ValidationStatus } from '$lib/services/api';
import { Checkbox } from "$lib/components/ui/checkbox";
import { Input } from "$lib/components/ui/input/index.js";

let placeholders = [
  "Ask anything...",
  "Try: 'Where was the VIN last located?'",
  "Try: 'What are the active faults?'",
  "Try: 'Give me the order details'"
];

let session = {
  user_id: '',
  session_id: '',
  model: 'groq/llama-3.3-70b-versatile',
  temperature: 0.45,
  enable_memory: false,
  session_messages: [] as { role: string, content: string, timestamp: string }[]
};

// Add error state
let error: string | null = null;
let isLoading = false;

$: modelGroups = Object.entries(PROVIDERS).map(([providerKey, provider]) => ({
  label: providerKey.charAt(0).toUpperCase() + providerKey.slice(1),
  options: Object.entries(provider.models).map(([modelKey, model]) => ({
    value: `${providerKey}/${modelKey}`,
    label: model.description
  }))
}));

// Helper to get selected model object
$: selectedModelObj = modelGroups.flatMap(g => g.options).find(o => o.value === session.model) || { value: session.model, label: session.model };
// Add sliderValue state
let sliderValue = [session.temperature];
$: if (sliderValue[0] !== session.temperature) sliderValue = [session.temperature];

// Vehicle details
let vinNumber = '';
let phoneNumber = '';
let orderId = '';

// Validation statuses
let vinStatus: ValidationStatus = 'secondary';
let phoneStatus: ValidationStatus = 'secondary';
let orderStatus: ValidationStatus = 'secondary';

// Validation messages
let vinMessage = 'No VIN provided';
let phoneMessage = 'No phone number provided';
let orderMessage = 'No order ID provided';

function randomId(prefix: string) {
  return `${prefix}-${Math.random().toString(36).slice(2, 10)}`;
}

onMount(async () => {
  if (auth.currentUser) {
    session.user_id = auth.currentUser.uid;
  } else {
    goto('/auth');
  }
  session.session_id = randomId('sess');

  // Initialize the API service
  await initApiService();
});

function handleNewSession() {
  session.session_id = randomId('sess');
  session.session_messages = [];
}

async function handleSubmit(prompt: string) {
  try {
    isLoading = true;
    error = null;

    // Add user message to the session
    session.session_messages = [
      ...session.session_messages,
      { role: 'user', content: prompt, timestamp: new Date().toISOString() }
    ];

    // Prepare the payload
    const payload = {
      prompt,
      model: session.model,
      temperature: session.temperature,
      user_id: session.user_id,
      session_id: session.session_id,
      enable_memory: session.enable_memory,
    };

    logger.info({ event: 'chat_request', payload });

    // Send the request using our API service
    const data = await sendChatRequest(payload);

    logger.info({ event: 'chat_response', data });

    // Add model response to the session
    session.session_messages = [
      ...session.session_messages,
      { role: 'model', content: data.response, timestamp: new Date().toISOString() }
    ];
  } catch (err) {
    const error = err as Error;
    logger.error({ event: 'chat_request_error', error });

    // Add error message to the session with more user-friendly content
    session.session_messages = [
      ...session.session_messages,
      {
        role: 'model',
        content: `I apologize, but I encountered an error: ${error.message || 'Failed to get response from the server'}. Please try rephrasing your question or try again later.`,
        timestamp: new Date().toISOString()
      }
    ];
  } finally {
    isLoading = false;
  }
}

async function handleLogout() {
  try {
    await signOut(auth);
    logger.info({ event: 'logout_success' });
    goto('/auth');
  } catch (error) {
    logger.error({ event: 'logout_error', error });
  }
}

// Validation handlers
async function handleVinChange() {
  const result = await validateVIN(vinNumber);
  vinStatus = result.status;
  vinMessage = result.message;
}

async function handlePhoneChange() {
  const result = await validatePhone(phoneNumber);
  phoneStatus = result.status;
  phoneMessage = result.message;
}

async function handleOrderIdChange() {
  const result = await validateOrderId(orderId);
  orderStatus = result.status;
  orderMessage = result.message;
}
</script>

<GridAndDotBackgrounds>
  <Spotlight>
    <div class="flex flex-col items-center justify-center min-h-screen relative">
      <Button on:click={toggleMode} variant="outline" size="icon" class="absolute top-4 right-16">
        <Sun class="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
        <Moon class="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
        <span class="sr-only">Toggle theme</span>
      </Button>
      <Button on:click={handleLogout} variant="outline" size="icon" class="absolute top-4 right-4">
        <LogOut class="h-[1.2rem] w-[1.2rem]" />
        <span class="sr-only">Logout</span>
      </Button>

      <!-- Header with added padding -->
      <h1 class="text-3xl font-bold my-10 py-4">Vahan Sahayak <sub class="text-base align-sub">Advanced</sub></h1>

      <!-- Main content container -->
      <div class="w-full max-w-5xl mx-auto flex flex-col gap-2 mb-4">
        {#if error}
          <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span class="block sm:inline">{error}</span>
          </div>
        {/if}

        <!-- Chat messages -->
        <div class="flex flex-col gap-2 mb-6">
          {#each session.session_messages as msg (msg.timestamp)}
            {#if msg.role === 'user'}
              <UserMessage content={msg.content} timestamp={msg.timestamp} user_id={session.user_id} user_name={auth.currentUser?.displayName || 'User'} />
            {:else}
              <ResponseMessage content={msg.content} timestamp={msg.timestamp} model={session.model} />
            {/if}
          {/each}
        </div>

        <!-- Chat input within the container -->
        <div class="mb-8">
          <VanishingChatSuggestions
            {placeholders}
            onSubmit={handleSubmit}
            className="w-full"
          />
        </div>

        <!-- Controls row -->
        <div class="w-full flex flex-row justify-between items-start gap-4 mb-10">

          <!-- Vehicle Information Fields (center) -->
          <div class="flex flex-col gap-3 flex-grow max-w-md">
            <!-- VIN Number -->
            <div class="flex flex-col gap-1">
              <Badge variant={vinStatus} class="w-full text-center">{vinMessage}</Badge>
              <div class="flex gap-2 items-center">
                <Input
                  type="text"
                  placeholder="Enter VIN Number"
                  bind:value={vinNumber}
                  on:input={handleVinChange}
                  class="w-full"
                />
              </div>
            </div>

            <!-- Phone Number -->
            <div class="flex flex-col gap-1">
              <Badge variant={phoneStatus} class="w-full text-center">{phoneMessage}</Badge>
              <div class="flex gap-2 items-center">
                <Input
                  type="text"
                  placeholder="Enter Phone Number"
                  bind:value={phoneNumber}
                  on:input={handlePhoneChange}
                  class="w-full"
                />
              </div>
            </div>

            <!-- Order ID -->
            <div class="flex flex-col gap-1">
              <Badge variant={orderStatus} class="w-full text-center">{orderMessage}</Badge>
              <div class="flex gap-2 items-center">
                <Input
                  type="text"
                  placeholder="Enter Order ID"
                  bind:value={orderId}
                  on:input={handleOrderIdChange}
                  class="w-full"
                />
              </div>
            </div>
          </div>

          <!-- LLM Controls (right) with added bottom margin -->
          <div class="flex flex-col items-end gap-2 mb-8">
            <div class="w-[260px]">
              <Badge variant="secondary" class="mb-2 w-[260px] text-center justify-center">{selectedModelObj.value}</Badge>
              <SelectRoot selected={selectedModelObj} onSelectedChange={v => v && (session.model = v.value)}>
                <SelectTrigger class="w-full">Select a model</SelectTrigger>
                <SelectContent side="top" align="end" class="max-h-60 overflow-y-auto">
                  {#each modelGroups as group}
                    <SelectGroup>
                      <SelectLabel>{group.label}</SelectLabel>
                      {#each group.options as option}
                        <SelectItem value={option.value} label={option.label}>
                          <div class="flex flex-col">
                            <span>{option.value.split('/').pop()}</span>
                            <span class="text-xs text-muted-foreground">{option.label}</span>
                          </div>
                        </SelectItem>
                      {/each}
                    </SelectGroup>
                  {/each}
                </SelectContent>
              </SelectRoot>
            </div>
            <div class="w-[260px] flex flex-col gap-2 mt-2">
              <!-- Session ID -->
              <div class="flex flex-col gap-2 w-full">
                <Badge variant="secondary" class="w-full text-center">{session.session_id}</Badge>
              </div>

              <!-- New Session and Memory Controls -->
              <div class="flex flex-col gap-2 w-full">
                <!-- New Session Button -->
                <Button id="new-session-button" on:click={handleNewSession} variant="outline" class="w-full">New Session</Button>

                <!-- Memory Toggle and Badge -->
                <div class="flex items-center gap-2">
                  <Checkbox id="memory-toggle" bind:checked={session.enable_memory} />
                  <Badge variant={session.enable_memory ? 'default' : 'secondary'} class="flex-1 text-center">
                    Memory {session.enable_memory ? 'On' : 'Off'}
                  </Badge>
                </div>
              </div>
              <!-- Temperature slider -->
              <label class="text-xs font-medium pb-2" for="temperature-slider">Temperature: {session.temperature.toFixed(2)}</label>
              <Slider id="temperature-slider" min={0} max={2} step={0.01} bind:value={sliderValue} on:change={() => session.temperature = sliderValue[0]} />

            </div>
          </div>
        </div>
      </div>
    </div>
  </Spotlight>
</GridAndDotBackgrounds>