[project]
name = "vahan-sahayak-fastapi-simple"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "asyncio>=3.4.3",
    "fastapi>=0.115.12",
    "google-genai>=1.14.0",
    "google-generativeai",
    "httpx>=0.28.1",
    "loguru>=0.7.3",
    "mcp>=1.8.0",
    "mem0ai>=0.1.97",
    "pydantic-ai-slim[groq,logfire,openai]>=0.1.10",
    "python-dotenv>=1.1.0",
    "supabase>=2.15.1",
    "uvicorn>=0.34.2",
    "vecs>=0.4.5",
]

[dependency-groups]
dev = [
    "pytest>=8.3.5",
    "ruff>=0.11.8",
]
