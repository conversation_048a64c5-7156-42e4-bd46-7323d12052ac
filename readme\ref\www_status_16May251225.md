This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

# File Summary

## Purpose
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

## File Format
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Multiple file entries, each consisting of:
  a. A header with the file path (## File: path/to/file)
  b. The full contents of the file in a code block

## Usage Guidelines
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

## Notes
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: www/*
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)

## Additional Info

# Directory Structure
```
www/chat_app.ts
www/chatapp.html
```

# Files

## File: www/chat_app.ts
```typescript
// BIG FAT WARNING: to avoid the complexity of npm, this typescript is compiled in the browser
// there's currently no static type checking

import { marked } from 'https://cdnjs.cloudflare.com/ajax/libs/marked/15.0.0/lib/marked.esm.js'
const convElement = document.getElementById('conversation')

const promptInput = document.getElementById('prompt-input') as HTMLInputElement | null
const spinner = document.getElementById('spinner')
const modelSelect = document.getElementById('model-select') as HTMLSelectElement | null
const temperatureSlider = document.getElementById('temperature-slider') as HTMLInputElement | null
const temperatureValue = document.getElementById('temperature-value') as HTMLSpanElement | null
const userIdInput = document.getElementById('user-id-input') as HTMLInputElement | null;
const sessionIdInput = document.getElementById('session-id-input') as HTMLInputElement | null;
const regenerateUserIdBtn = document.getElementById('regenerate-user-id') as HTMLButtonElement | null;
const regenerateSessionIdBtn = document.getElementById('regenerate-session-id') as HTMLButtonElement | null;
const memoryToggle = document.getElementById('memory-toggle') as HTMLInputElement | null;

// stream the response and render messages as each chunk is received
// data is sent as newline-delimited JSON
async function onFetchResponse(response: Response): Promise<void> {
  let text = ''
  let decoder = new TextDecoder()
  if (response.ok) {
    if (!response.body) return;
    const reader = response.body.getReader()
    while (true) {
      const {done, value} = await reader.read()
      if (done) {
        break
      }
      text += decoder.decode(value)
      addMessages(text)
      if (spinner) spinner.classList.remove('active')
    }
    addMessages(text)
    if (promptInput) {
      promptInput.disabled = false
      promptInput.focus()
    }
  } else {
    const text = await response.text()
    console.error(`Unexpected response: ${response.status}`, {response, text})
    throw new Error(`Unexpected response: ${response.status}`)
  }
}

// The format of messages, this matches pydantic-ai both for brevity and understanding
// in production, you might not want to keep this format all the way to the frontend
interface Message {
  role: string
  content: string
  timestamp: string
}

// take raw response text and render messages into the `#conversation` element
// Message timestamp is assumed to be a unique identifier of a message, and is used to deduplicate
// hence you can send data about the same message multiple times, and it will be updated
// instead of creating a new message elements
function addMessages(responseText: string) {
  if (!convElement) return;
  const lines = responseText.split('\n')
  const messages: Message[] = lines.filter(line => line.length > 1).map(j => JSON.parse(j))
  for (const message of messages) {
    // we use the timestamp as a crude element id
    const {timestamp, role, content} = message
    const id = `msg-${timestamp}`
    let msgDiv = document.getElementById(id)
    if (!msgDiv) {
      msgDiv = document.createElement('div')
      msgDiv.id = id
      msgDiv.title = `${role} at ${timestamp}`
      msgDiv.classList.add('border-top', 'pt-2', role)
      convElement.appendChild(msgDiv)
    }
    msgDiv.innerHTML = marked.parse(content)
  }
  window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' })
}

function onError(error: any) {
  console.error(error)
  const errorDiv = document.getElementById('error')
  if (errorDiv) errorDiv.classList.remove('d-none')
  const spinnerDiv = document.getElementById('spinner')
  if (spinnerDiv) spinnerDiv.classList.remove('active')
}

function randomId(prefix: string) {
  return prefix + '-' + Math.random().toString(36).slice(2, 10);
}

function setRandomUserId() {
  if (userIdInput) userIdInput.value = randomId('user');
}
function setRandomSessionId() {
  if (sessionIdInput) sessionIdInput.value = randomId('sess');
}

function clearConversation() {
  if (convElement) {
    convElement.innerHTML = '';
  }
}

function setRandomUserIdAndClear() {
  setRandomUserId();
  clearConversation();
}
function setRandomSessionIdAndClear() {
  setRandomSessionId();
  clearConversation();
}

if (regenerateUserIdBtn) regenerateUserIdBtn.addEventListener('click', setRandomUserIdAndClear);
if (regenerateSessionIdBtn) regenerateSessionIdBtn.addEventListener('click', setRandomSessionIdAndClear);
// Set initial values on page load
setRandomUserId();
setRandomSessionId();

function fetchSessionChatHistory(sessionId: string) {
  fetch(`http://localhost:8000/chat/${encodeURIComponent(sessionId)}/`)
    .then(onFetchResponse)
    .catch(onError);
}

if (sessionIdInput) {
  sessionIdInput.addEventListener('change', () => {
    if (sessionIdInput.value) {
      fetchSessionChatHistory(sessionIdInput.value);
    }
  });
}

// On page load, fetch chat history for the initial session
if (sessionIdInput && sessionIdInput.value) {
  fetchSessionChatHistory(sessionIdInput.value);
}

async function onSubmit(e: SubmitEvent): Promise<void> {
  e.preventDefault()
  if (!spinner || !promptInput || !modelSelect || !temperatureSlider || !userIdInput || !sessionIdInput || !memoryToggle) return;
  spinner.classList.add('active')
  const prompt = promptInput.value
  const model = modelSelect.value
  const temperature = parseFloat(temperatureSlider.value)
  const user_id = userIdInput.value
  const session_id = sessionIdInput.value
  const enable_memory = memoryToggle.checked;

  const body = JSON.stringify({
    user_id: user_id,
    session_id: session_id,
    prompt: prompt,
    model: model,
    temperature: temperature,
    enable_memory: enable_memory
  })

  promptInput.value = ''
  promptInput.disabled = true

  const response = await fetch(`http://localhost:8000/chat/${encodeURIComponent(session_id)}/`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body
  })
  await onFetchResponse(response)
}

// call onSubmit when the form is submitted (e.g. user clicks the send button or hits Enter)
document.querySelector('form')?.addEventListener('submit', (e) => onSubmit(e).catch(onError))

if (temperatureSlider && temperatureValue) {
  temperatureSlider.addEventListener('input', () => {
    temperatureValue.textContent = parseFloat(temperatureSlider.value).toFixed(2);
  });
}
```

## File: www/chatapp.html
```html
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vahan Sahayak Instant</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    main {
      max-width: 700px;
    }
    #conversation .user::before {
      content: 'You asked: ';
      font-weight: bold;
      display: block;
    }
    #conversation .model::before {
      content: 'AI Response: ';
      font-weight: bold;
      display: block;
    }
    #spinner {
      opacity: 0;
      transition: opacity 500ms ease-in;
      width: 30px;
      height: 30px;
      border: 3px solid #222;
      border-bottom-color: transparent;
      border-radius: 50%;
      animation: rotation 1s linear infinite;
    }
    @keyframes rotation {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    #spinner.active {
      opacity: 1;
    }
  </style>
</head>
<body>
  <main class="border rounded mx-auto my-5 p-4">
    <h1>Vahan Sahayak Instant</h1>
    <p>Ask me anything...</p>
    <div id="conversation" class="px-2"></div>
    <div class="d-flex justify-content-center mb-3">
      <div id="spinner"></div>
    </div>
    <form method="post">
      <div class="mb-2 d-flex align-items-center">
        <label for="user-id-input" class="form-label me-2 mb-0">User ID</label>
        <input id="user-id-input" name="user_id" class="form-control me-2" style="max-width: 200px;" />
        <button type="button" class="btn btn-outline-secondary btn-sm" id="regenerate-user-id">Regenerate</button>
      </div>
      <div class="mb-2 d-flex align-items-center">
        <label for="session-id-input" class="form-label me-2 mb-0">Session ID</label>
        <input id="session-id-input" name="session_id" class="form-control me-2" style="max-width: 200px;" />
        <button type="button" class="btn btn-outline-secondary btn-sm" id="regenerate-session-id">Regenerate</button>
      </div>
      <div class="mb-2">
        <label for="model-select" class="form-label">Model</label>
        <select id="model-select" name="model" class="form-select">
          <optgroup label="Groq">
            <option value="groq/llama-3.3-70b-versatile">Groq: Llama 3.3 70B - Meta's versatile large language model</option>
            <option value="groq/meta-llama/llama-4-maverick-17b-128e-instruct">Groq: Llama 4 Maverick 17B - Meta's latest instruction-tuned model</option>
            <option value="groq/deepseek-r1-distill-llama-70b">Groq: DeepSeek R1 Distill Llama 70B - DeepSeek's compact instruction-tuned model</option>
          </optgroup>
          <optgroup label="OpenRouter">
            <option value="openrouter/anthropic/claude-3.5-sonnet">OpenRouter: Claude 3.5 Sonnet - Anthropic's powerful and efficient model</option>
            <option value="openrouter/anthropic/claude-3.7-sonnet">OpenRouter: Claude 3.7 Sonnet - Anthropic's latest and most capable model</option>
            <option value="openrouter/meta-llama/llama-3.1-8b-instruct">OpenRouter: Llama 3.1 8B - Meta's compact instruction-tuned model</option>
          </optgroup>
        </select>
      </div>
      <div class="mb-2">
        <label for="temperature-slider" class="form-label">Temperature: <span id="temperature-value">0.2</span></label>
        <input type="range" class="form-range" min="0" max="2" step="0.01" value="0.2" id="temperature-slider" name="temperature" />
      </div>
      <div class="mb-2 form-check">
        <input class="form-check-input" type="checkbox" id="memory-toggle" name="enable_memory" checked>
        <label class="form-check-label" for="memory-toggle">Enable Chat Memory</label>
      </div>
      <input id="prompt-input" name="prompt" class="form-control"/>
      <div class="d-flex justify-content-end">
        <button class="btn btn-primary mt-2">Send</button>
      </div>
    </form>
    <div id="error" class="d-none text-danger">
      Error occurred, check the browser developer console for more information.
    </div>
  </main>
</body>
</html>
<script src="https://cdnjs.cloudflare.com/ajax/libs/typescript/5.6.3/typescript.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script type="module">
  // to let me write TypeScript, without adding the burden of npm we do a dirty, non-production-ready hack
  // and transpile the TypeScript code in the browser
  // this is (arguably) A neat demo trick, but not suitable for production!
  async function loadTs() {
    const response = await fetch('../chat_app.ts');
    const tsCode = await response.text();
    const jsCode = window.ts.transpile(tsCode, { target: "es2015" });
    let script = document.createElement('script');
    script.type = 'module';
    script.text = jsCode;
    document.body.appendChild(script);
  }

  loadTs().catch((e) => {
    console.error(e);
    document.getElementById('error').classList.remove('d-none');
    document.getElementById('spinner').classList.remove('active');
  });
</script>
```
