- src/integrations/tool_module.py depends on:
  - supabase (for Supabase client)
  - pydantic_ai (for Tool)
  - loguru (for logging)
  - dotenv (for environment variables)
  - os (for file and env handling)
  - google.generativeai (for Gemini embeddings)
- src/integrations/mem0_module.py depends on:
  - mem0 (Memory)
  - logging (standard library)
- No longer depends on dotenv or MemoryClient
- No collection or advanced config logic 