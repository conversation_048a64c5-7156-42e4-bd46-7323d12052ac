from loguru import logger
from pydantic_ai.mcp import MCPServerHTTP

import httpx
import asyncio
from typing import List, Optional

from dotenv import load_dotenv
import os
load_dotenv()

# Logging setup as per project rules
LOG_DIR = os.path.join(os.path.dirname(__file__), '../../logs/mcp_module')
os.makedirs(LOG_DIR, exist_ok=True)
logger.add(os.path.join(LOG_DIR, 'info.log'), level='INFO', rotation='10 MB', retention='10 days')
logger.add(os.path.join(LOG_DIR, 'debug.log'), level='DEBUG', rotation='10 MB', retention='10 days')
# MCPServerHTTP(url=telematics_mcp_url, timeout=10, sse_read_timeout=300)


class MCPManager:
    def __init__(self):
        self.telematics_host = os.getenv('TELEMATICS_MCP_HOST_IP_PORT')
        self.supabase_host = os.getenv('SUPABASE_MCP_HOST_IP_PORT')
        # Default timeout values
        self.timeout = 10  # Connection timeout in seconds
        self.sse_read_timeout = 300  # SSE read timeout in seconds

    def get_host_list(self) -> List[str]:
        """Get a list of configured MCP host addresses.
        
        Returns:
            List of host addresses that are not None.
        """
        hosts = [self.telematics_host, self.supabase_host]
        # Filter out None values
        return [host for host in hosts if host]

    async def validate_mcp_server(self, host: str) -> Optional[MCPServerHTTP]:
        """Validate if an MCP server is running and properly configured.
        
        Args:
            host: The host address to validate.
            
        Returns:
            MCPServerHTTP instance if server is valid, None otherwise.
        """
        if not host:
            return None
            
        try:
            logger.info(f"Checking host: {host}")
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                # Check health endpoint
                response = await client.get(f'http://{host}/health')
                
                if response.status_code == 200:
                    # Now validate the SSE endpoint with a HEAD request
                    try:
                        sse_url = f'http://{host}/sse'
                        await client.head(sse_url)
                        logger.info(f"MCP server validated: {host}")
                        return MCPServerHTTP(
                            url=sse_url, 
                            timeout=self.timeout, 
                            sse_read_timeout=self.sse_read_timeout
                        )
                    except Exception as e:
                        logger.error(f"SSE endpoint validation failed for {host}: {e}")
                else:
                    logger.warning(f"Health check failed for {host}: Status code {response.status_code}")
        except Exception as e:
            logger.error(f"Error checking host {host}: {e}")
            
        return None

    async def get_mcp_servers(self) -> List[MCPServerHTTP]:
        """Get a list of valid MCP servers.
        
        Returns:
            List of validated MCPServerHTTP instances.
        """
        host_list = self.get_host_list()
        if not host_list:
            logger.warning("No MCP hosts configured in environment variables")
            return []
            
        tasks = [self.validate_mcp_server(host) for host in host_list]
        servers = await asyncio.gather(*tasks)
        
        # Filter out None values
        available_servers = [server for server in servers if server]
        
        if not available_servers:
            logger.warning("No MCP servers are available")
        else:
            logger.info(f"Found {len(available_servers)} available MCP servers")
            
        return available_servers


if __name__ == "__main__":
    mcp_manager = MCPManager()
    print(asyncio.run(mcp_manager.get_mcp_servers()))

