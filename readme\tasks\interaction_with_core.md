# Interacting with `core.py` using Insomnia

This guide will help you interact with the FastAPI application defined in `core.py` using Insomnia, a popular API client.

## Prerequisites

- Ensure that the FastAPI server is running. You can start the server by executing the following command in your terminal:
  ```bash
  python src/core.py
  ```
  By default, the server will run on `http://0.0.0.0:8000`.

- Download and install [Insomnia](https://insomnia.rest/download) if you haven't already.

## Setting Up Insomnia

1. **Create a New Request Collection:**
   - Open Insomnia and click on the "Create" button.
   - Select "Request Collection" and name it something like "Chat App API".

2. **Add Requests:**

   ### Health Check Endpoint
   - **Request Name:** Health Check
   - **Method:** GET
   - **URL:** `http://0.0.0.0:8000/health`
   - **Description:** This endpoint checks the health status of the server. It should return `{"status": "ok"}` if the server is running properly.

   ### Get Chat Messages
   - **Request Name:** Get Chat Messages
   - **Method:** GET
   - **URL:** `http://0.0.0.0:8000/chat/`
   - **Description:** Fetches all chat messages stored in the database.
   - **Example Response:**
     ```json
     [
       {
         "role": "user",
         "timestamp": "2023-10-01T12:00:00Z",
         "content": "Hello, how are you?"
       },
       {
         "role": "model",
         "timestamp": "2023-10-01T12:00:01Z",
         "content": "I'm good, thank you! How can I assist you today?"
       }
     ]
     ```

   ### Post Chat Message
   - **Request Name:** Post Chat Message
   - **Method:** POST
   - **URL:** `http://0.0.0.0:8000/chat/`
   - **Body Type:** Form URL Encoded
   - **Form Data:**
     - **Key:** `prompt`
     - **Value:** (Enter your chat message here)
   - **Description:** Sends a new chat message to the server. The server will process the message and return a streaming response with the chat history and the model's response.
   - **Example Request:**
     - **Prompt:** "What is the weather like today?"
   - **Example Response:**
     ```json
     {
       "role": "user",
       "timestamp": "2023-10-01T12:05:00Z",
       "content": "What is the weather like today?"
     }
     {
       "role": "model",
       "timestamp": "2023-10-01T12:05:01Z",
       "content": "The weather today is sunny with a high of 25°C."
     }
     ```

   ### Get Main TypeScript File
   - **Request Name:** Get Main TypeScript File
   - **Method:** GET
   - **URL:** `http://0.0.0.0:8000/chat_app.ts`
   - **Description:** Retrieves the raw TypeScript code used in the chat application.

3. **Testing the Endpoints:**
   - Click on each request in Insomnia and hit the "Send" button to test the endpoints.
   - Verify the responses to ensure the server is functioning as expected.

## Example: Posting a Chat Message Using Insomnia

You can interact with the `/chat/` POST endpoint (see `post_chat` in `src/core.py`) using Insomnia as follows:

1. **Create a New Request:**
   - Click the "New Request" button in Insomnia.
   - Name it (e.g., "Post Chat Message").
   - Set the method to `POST`.
   - Set the URL to `http://0.0.0.0:8000/chat/`.

2. **Configure the Request Body:**
   - Select the `Body` tab.
   - Choose `Form URL Encoded`.
   - Add a key called `prompt` and set its value to your message (e.g., `What is the weather like today?`).
   - **Note:** The backend (`src/core.py`, see `post_chat`) expects the `prompt` field as form data, matching how the frontend (`src/chat_app.ts`) sends it using `FormData`.

---
**Troubleshooting:**
- If you receive an error like:
  ```json
  {
    "detail": [
      {
        "type": "missing",
        "loc": ["body", "prompt"],
        "msg": "Field required",
        "input": null
      }
    ]
  }
  ```
  This means the `prompt` field was missing from your request body. Double-check that you are using `Form URL Encoded` and have a key named `prompt` with a non-empty value. This is required by the FastAPI backend (`src/core.py`) and is how the browser client (`src/chat_app.ts`) works as well.

3. **Send the Request:**
   - Click "Send".
   - You will receive a streaming response with each message as a newline-delimited JSON object.

**Example Request:**
- Method: `POST`
- URL: `http://0.0.0.0:8000/chat/`
- Body (Form URL Encoded):
  - `prompt`: `What is the weather like today?`

**Example Streaming Response:**
```json
{"role": "user", "timestamp": "2023-10-01T12:05:00Z", "content": "What is the weather like today?"}
{"role": "model", "timestamp": "2023-10-01T12:05:01Z", "content": "The weather today is sunny with a high of 25°C."}
```

This endpoint is implemented in `src/core.py` as the `post_chat` function.

## Conclusion

By following these steps, you can easily interact with the FastAPI application using Insomnia. This setup allows you to test the API endpoints and ensure that the chat application is working correctly.



