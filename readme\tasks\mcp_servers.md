# MCP Servers in Vahan Sahayak

## Overview

MCP (Model Control Protocol) servers are used to provide additional capabilities to our LLM agents through external tools and services. The agent system has been designed to support MCP servers while maintaining performance by separating the health checking and initialization processes.

## Architecture

1. **Health Checking at Startup**:
   - MCP server health is checked when the FastAPI application starts
   - Results are stored in `src/config/integrations.json`
   - This prevents redundant health checks for each agent creation

2. **Agent Creation**:
   - `get_agent()` is now synchronous and reads MCP server information from the JSON file
   - MCP servers are included in the agent configuration but not initialized yet

3. **MCP Server Initialization**:
   - MCP servers must be initialized within a proper context manager using `async with agent.run_mcp_servers()`
   - All operations that need MCP functionality must be performed within this context

## Usage

To use MCP servers with an agent:

```python
# Create agent (synchronous)
agent_module = AgentModule("provider/model_name")
agent = agent_module.get_agent()

# Use MCP servers within a context manager
async with agent.run_mcp_servers():
    # Perform operations that need MCP functionality
    result = await agent.run("Your prompt")
    # Process result...
```

## API Endpoints

The FastAPI endpoints have been updated to:
1. Create the agent synchronously
2. Use the MCP server context manager to wrap the entire operation
3. Process all agent operations within this context

## Example: Chat Endpoint

```python
@app.post('/chat/')
async def post_chat(request: RequestModel, database: Database) -> StreamingResponse:
    # Create agent (synchronous)
    agent_module = AgentModule(request.model)
    agent = agent_module.get_agent()
    
    async def stream_messages():
        # Initial user message...
        
        # Use MCP servers within a proper context manager
        async with agent.run_mcp_servers():
            async with agent.run_stream(request.prompt) as result:
                # Process streaming response...
    
    return StreamingResponse(stream_messages())
```

## Performance Considerations

This approach provides several benefits:
- Agent creation is fast and doesn't block on network requests
- MCP servers are only active when needed and within a proper context
- Integration status is cached for reuse across multiple requests
- Health checks happen at application startup, not per-request

## Refreshing MCP Server Status

To refresh the status of MCP servers:

```
POST /integrations/refresh
```

This endpoint will perform health checks on all integrations and update the status file. 