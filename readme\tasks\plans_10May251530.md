# Vahan Sahayak Svelte Port: Migration Plan

## Note
SvelteKit project base, Tailwind CSS, <PERSON><PERSON><PERSON>, ESLint, and path aliases are already set up. Proceeding with component and route scaffolding.

## Immediate Next Steps
- Create `src/lib/components` directory for UI components
- Scaffold `/auth` and `/chat` routes
- Set up Shadcn-Svelte and Aceternity UI for ready-made components

## Overview
This plan outlines the steps to port the Vahan Sahayak UI from Next.js (React) to SvelteKit, leveraging modern Svelte UI libraries:
- [Shadcn-SvelteKit](https://www.shadcn-svelte.com/docs/installation/sveltekit)
- [Aceternity Svelte UI](https://aceternity.sveltekit.io/)

## Migration Phases

### 1. Project Setup
- Initialize new SvelteKit project (TypeScript, pnpm)
- Add Tailwind CSS using svelte-add
- Set up Pre<PERSON><PERSON>, ESL<PERSON>, and <PERSON><PERSON> (if needed)
- Configure path aliases
- Initialize git (if not already)

### 2. UI Library Integration
- Install and configure Shadcn-SvelteKit
- Install and configure Aceternity UI (for modern/polished components)
- Set up Tailwind config for both libraries

### 3. Core Architecture
- Set up SvelteKit routing to match current app
- Implement Svelte stores for global state (user/session, chat state)
- Set up environment variables and API config

### 4. Component Porting
- Port UI primitives (Button, Card, Input, etc.) using Shadcn-Svelte/Aceternity
- Port layout and page structure
- Port chat interface and related controls
- Port model selection and temperature controls

### 5. Service & Logic Porting
- Port API service logic (API calls, error handling)
- Port model definitions and selection logic
- Port chat streaming and message formatting logic

### 6. Feature Parity
- Implement authentication (if needed)
- Implement chat session management
- Implement markdown rendering for messages
- Implement responsive design

### 7. Testing & QA
- Manual and automated testing
- Accessibility and performance checks

### 8. Documentation & Handover
- Update README and developer docs
- Task tracking and progress updates

## References
- [Shadcn-SvelteKit Docs](https://www.shadcn-svelte.com/docs/installation/sveltekit)
- [Aceternity Svelte UI](https://aceternity.sveltekit.io/) 