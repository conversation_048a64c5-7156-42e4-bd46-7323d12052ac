# Python UV Best Practices

## Python Typing

### Type Annotations
- Always use type hints for function parameters and return values
- Use appropriate typing imports: `from typing import List, Dict, Optional, Union, Callable, Any, TypeVar, Generic`
- For Python 3.9+, use built-in collection types for annotations: `list[str]` instead of `List[str]`
- Use `Optional[T]` when a value can be `None`
- Use `Union[Type1, Type2]` when a value can be multiple types
- For callbacks, define the function signature with `Callable[[param_types], return_type]`

### Type Variables
- Use `TypeVar` for generic functions and classes
- Name type variables with descriptive uppercase names (e.g., `T`, `K`, `V`, `TKey`, `TValue`)

### Type Comments
- Add type comments for complex expressions: `# type: List[Dict[str, Any]]`
- Use `# type: ignore` sparingly and only when necessary

## Docstrings

### Format
- Use Google-style docstrings
- Include parameter types and return types in docstrings
- Document exceptions raised
- Add examples for complex functions
- Keep docstrings concise yet informative

### Example
```python
def fetch_data(url: str, timeout: Optional[int] = None) -> dict:
    """Fetches data from the given URL.
    
    Args:
        url: The URL to fetch data from.
        timeout: Request timeout in seconds. Default is None.
        
    Returns:
        The parsed JSON response as a dictionary.
        
    Raises:
        RequestError: If the request fails.
        TimeoutError: If the request times out.
        
    Example:
        >>> data = fetch_data("https://api.example.com/data")
        >>> print(data["status"])
    """
    # Implementation
```

## Refactoring and Separation of Concerns (SoC)

### Code Organization
- Organize code into logical modules and packages
- Group related functionality together
- Use clear, descriptive module and package names
- Follow the Single Responsibility Principle (SRP)
- Keep modules focused on a single aspect of functionality

### Function and Class Design
- Keep functions small and focused on a single task
- Limit function parameters to 3-4 maximum
- Use data classes or named tuples for complex parameter groups
- Extract repeated code into helper functions
- Use composition over inheritance when possible

### Refactoring Techniques
- Extract method: Break large functions into smaller, focused ones
- Extract class: Move related functionality into dedicated classes
- Replace conditional with polymorphism: Use inheritance or strategy pattern
- Replace magic numbers with named constants
- Use early returns to reduce nesting and improve readability

### Example of Good Separation of Concerns
```python
# Bad: Mixed responsibilities
def process_user_data(user_id: str) -> dict:
    # Fetches data, processes it, and sends notifications all in one function
    user_data = fetch_from_database(user_id)
    processed_data = calculate_metrics(user_data)
    send_notification(user_id, processed_data)
    return processed_data

# Good: Separated concerns
def get_user_data(user_id: str) -> dict:
    """Fetches user data from the database."""
    return fetch_from_database(user_id)

def process_data(data: dict) -> dict:
    """Processes raw user data into metrics."""
    return calculate_metrics(data)

def notify_user(user_id: str, data: dict) -> None:
    """Sends notification to user about processed data."""
    send_notification(user_id, data)

def process_user_data(user_id: str) -> dict:
    """Orchestrates the user data processing workflow."""
    user_data = get_user_data(user_id)
    processed_data = process_data(user_data)
    notify_user(user_id, processed_data)
    return processed_data
```

### Refactoring
- make sure files do not get blotted and are split based on SoC